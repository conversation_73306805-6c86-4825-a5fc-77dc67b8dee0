import os
from typing import Optional

class Config:
    """配置管理类"""
    
    @staticmethod
    def get_api_key() -> Optional[str]:
        """获取API密钥"""
        # 优先从环境变量获取
        api_key = os.getenv('OPENROUTER_API_KEY')
        if api_key:
            return api_key
        
        # 从配置文件获取
        try:
            with open('.env', 'r') as f:
                for line in f:
                    if line.startswith('OPENROUTER_API_KEY='):
                        return line.split('=', 1)[1].strip()
        except FileNotFoundError:
            pass
        
        return None
    
    @staticmethod
    def get_csv_path() -> str:
        """获取CSV文件路径"""
        return os.getenv('CSV_FILE_PATH', '/Users/<USER>/Documents/code/测试用/bertdata/bert0530.csv')