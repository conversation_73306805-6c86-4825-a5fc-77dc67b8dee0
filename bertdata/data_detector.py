import os
import pandas as pd
import json
import threading
import time
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from typing import List, Tuple
import logging
from openai import OpenAI
from config import Config
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志 - 禁用HTTP相关日志
logging.getLogger("openai").setLevel(logging.WARNING)
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("requests").setLevel(logging.WARNING)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataDetector:
    def __init__(self, api_key: str, csv_file_path: str, batch_size: int = 10, max_workers: int = 10):
        self.client = OpenAI(
            api_key=api_key,
            base_url="https://openrouter.ai/api/v1"
        )
        self.model = "google/gemini-2.5-flash-preview-05-20"
        self.csv_file_path = csv_file_path
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.lock = threading.Lock()
        
        # 输出文件路径
        self.output_file = self.csv_file_path.replace('.csv', '_detected.csv')
        
        # 进度文件路径
        self.progress_file = self.csv_file_path.replace('.csv', '_progress.json')
        
        self.system_prompt = """
你是一个专门用于检测游戏聊天内容中违规行为的AI助手。你的任务是分析聊天消息，识别是否包含以下违规内容：

1. **RMT (Real Money Trading)** - 真实货币交易相关内容
2. **联系方式索取/分享** - 试图获取或分享外部联系方式
3. **账号买卖** - 游戏账号交易相关内容

**标注规则：**
- **标签 1（违规）**：包含上述任何违规内容
- **标签 0（正常）**：正常的游戏聊天内容

**关键违规指标：**
- 价格相关："多少钱"、"￥"、"元"、数字+货币单位
- 联系方式："V"、"WX"、"QQ"、"微信"、"加我"、"私聊"
- 交易意图："出售"、"购买"、"代练"、"充值"、"便宜"
- 外部引导："群"、"裙"、"加群"、"拉你"

**输出格式**：严格按照以下格式输出，每行一个结果：
标签: 0, 置信度: 0.95
标签: 1, 置信度: 0.87

请确保：
1. 每行只包含一个结果
2. 标签只能是0或1
3. 置信度是0到1之间的小数
4. 不要添加任何额外的解释文本
        """.strip()
    
    def load_data(self) -> pd.DataFrame:
        """加载CSV数据"""
        try:
            df = pd.read_csv(self.csv_file_path)
            
            # 初始化结果列
            if 'predicted_label' not in df.columns:
                df['predicted_label'] = ''
            if 'confidence' not in df.columns:
                df['confidence'] = 0.0
                
            logger.info(f"成功加载数据，共 {len(df)} 行")
            return df
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise
    
    def load_progress(self) -> dict:
        """加载处理进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载进度文件失败: {e}")
        return {'completed_batches': [], 'total_batches': 0}
    
    def save_progress(self, completed_batches: List[int], total_batches: int):
        """保存处理进度"""
        try:
            progress = {
                'completed_batches': completed_batches,
                'total_batches': total_batches,
                'last_update': time.time()
            }
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存进度失败: {e}")
    
    def get_pending_batches(self, total_batches: int) -> List[int]:
        """获取待处理的批次"""
        progress = self.load_progress()
        completed = set(progress.get('completed_batches', []))
        pending = [i for i in range(total_batches) if i not in completed]
        
        if completed:
            logger.info(f"发现已完成的批次: {len(completed)} 个，待处理: {len(pending)} 个")
        
        return pending
    
    def call_api(self, messages: List[str]) -> List[Tuple[str, float]]:
        """调用OpenRouter API（使用OpenAI兼容格式）"""
        try:
            # 构建用户消息
            user_content = "\n".join([f"{i+1}. {msg}" for i, msg in enumerate(messages)])
            
            # 使用OpenAI兼容客户端
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_content}
                ],
                temperature=0.1,
                max_tokens=1000
            )
            
            content = response.choices[0].message.content
            
            # 解析响应
            return self.parse_response(content, len(messages))
            
        except Exception as e:
            logger.error(f"API调用失败: {e}")
            # 返回默认值
            return [("0", 0.5) for _ in messages]
    
    def parse_response(self, content: str, expected_count: int) -> List[Tuple[str, float]]:
        """解析API响应 - 改进版本，增强鲁棒性"""
        import re
        
        results = []
        lines = content.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 尝试提取标签和置信度
            label, confidence = self._extract_label_confidence(line)
            if label is not None:
                results.append((label, confidence))
        
        # 确保结果数量正确
        while len(results) < expected_count:
            results.append(("0", 0.5))
        
        return results[:expected_count]
    
    def _extract_label_confidence(self, line: str) -> Tuple[str, float]:
        """从单行文本中提取标签和置信度"""
        import re
        
        try:
            # 模式1: 标准格式 "标签: X, 置信度: Y"
            if '标签:' in line and '置信度:' in line:
                # 使用正则表达式更精确地提取
                label_match = re.search(r'标签[：:]\s*([01])', line)
                confidence_match = re.search(r'置信度[：:]\s*([0-9]*\.?[0-9]+)', line)
                
                if label_match:
                    label = label_match.group(1).strip()
                    confidence = 0.5  # 默认置信度
                    
                    if confidence_match:
                        try:
                            confidence = float(confidence_match.group(1))
                            # 确保置信度在合理范围内
                            confidence = max(0.0, min(1.0, confidence))
                        except ValueError:
                            confidence = 0.5
                    
                    return label, confidence
            
            # 模式2: 简化格式，只有数字
            number_match = re.search(r'^\s*([01])\s*$', line)
            if number_match:
                return number_match.group(1), 0.8
            
            # 模式3: 其他可能的格式
            if '1' in line and ('高' in line or 'high' in line.lower()):
                return "1", 0.9
            elif '0' in line and ('低' in line or 'low' in line.lower()):
                return "0", 0.9
            
            return None, None
            
        except Exception as e:
            logger.warning(f"解析行时出错: {line}, 错误: {e}")
            return None, None
    
    def process_batch(self, batch_idx: int, batch_data: List[Tuple[int, str]]) -> List[Tuple[int, str, float]]:
        """处理一批数据"""
        indices, messages = zip(*batch_data)
        
        try:
            results = self.call_api(list(messages))
            
            batch_results = []
            for i, (label, confidence) in enumerate(results):
                batch_results.append((indices[i], label, confidence))
            
            logger.info(f"成功处理批次 {batch_idx}，索引范围: {min(indices)}-{max(indices)}")
            return batch_results
            
        except Exception as e:
            logger.error(f"处理批次 {batch_idx} 失败: {e}")
            # 返回默认结果
            return [(idx, "0", 0.5) for idx in indices]
    
    def save_batch_results(self, df: pd.DataFrame, results: List[Tuple[int, str, float]]):
        """保存批次结果到CSV文件（动态写入）"""
        try:
            with self.lock:
                # 更新DataFrame
                for idx, label, confidence in results:
                    df.loc[idx, 'predicted_label'] = label
                    df.loc[idx, 'confidence'] = confidence
                
                # 立即保存到文件
                df.to_csv(self.output_file, index=False)
                
        except Exception as e:
            logger.error(f"保存批次结果失败: {e}")
    
    def run(self):
        """主执行函数 - 支持断点续传和动态写入"""
        logger.info("开始数据检测任务")
        
        # 加载数据
        df = self.load_data()
        
        # 准备批次数据
        batches = []
        for i in range(0, len(df), self.batch_size):
            batch = []
            for j in range(i, min(i + self.batch_size, len(df))):
                chat_content = str(df.iloc[j]['chat_content'])
                batch.append((j, chat_content))
            batches.append(batch)
        
        total_batches = len(batches)
        logger.info(f"总共 {total_batches} 个批次，每批次 {self.batch_size} 条数据")
        
        # 获取待处理的批次
        pending_batch_indices = self.get_pending_batches(total_batches)
        
        if not pending_batch_indices:
            logger.info("所有批次已处理完成")
            return
        
        logger.info(f"开始处理 {len(pending_batch_indices)} 个待处理批次")
        
        # 加载已完成的批次列表
        progress = self.load_progress()
        completed_batches = set(progress.get('completed_batches', []))
        
        # 多线程处理待处理的批次
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交待处理的任务
            future_to_batch_idx = {
                executor.submit(self.process_batch, batch_idx, batches[batch_idx]): batch_idx 
                for batch_idx in pending_batch_indices
            }
            
            # 收集结果并动态保存
            for future in as_completed(future_to_batch_idx):
                batch_idx = future_to_batch_idx[future]
                try:
                    batch_results = future.result()
                    
                    # 立即保存这批结果
                    self.save_batch_results(df, batch_results)
                    
                    # 更新进度
                    completed_batches.add(batch_idx)
                    self.save_progress(list(completed_batches), total_batches)
                    
                    logger.info(f"进度: {len(completed_batches)}/{total_batches} 批次完成")
                    
                except Exception as e:
                    logger.error(f"批次 {batch_idx} 处理异常: {e}")
        
        # 清理进度文件
        if len(completed_batches) == total_batches:
            try:
                os.remove(self.progress_file)
                logger.info("所有批次处理完成，已清理进度文件")
            except:
                pass
        
        logger.info(f"数据检测任务完成，结果已保存到: {self.output_file}")


def main():
    # 配置参数 - 使用配置类读取
    API_KEY = Config.get_api_key()
    CSV_FILE = Config.get_csv_path()
    
    if not API_KEY or API_KEY == "your_openrouter_api_key_here":
        logger.error("请在 .env 文件中设置 OPENROUTER_API_KEY")
        return
    
    # 创建检测器并运行
    detector = DataDetector(
        api_key=API_KEY,
        csv_file_path=CSV_FILE,
        batch_size=10,
        max_workers=10
    )
    
    detector.run()


if __name__ == "__main__":
    main()