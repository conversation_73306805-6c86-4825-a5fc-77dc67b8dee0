#!/bin/bash

# MP4转M4A转换脚本
# 使用ffmpeg将MP4视频文件转换为M4A音频文件

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查ffmpeg是否安装
check_ffmpeg() {
    if ! command -v ffmpeg &> /dev/null; then
        echo -e "${RED}错误：系统中未找到ffmpeg工具${NC}"
        echo "请先安装ffmpeg："
        echo "  macOS: brew install ffmpeg"
        echo "  Ubuntu: sudo apt install ffmpeg"
        echo "  Windows: 从 https://ffmpeg.org/download.html 下载"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "MP4转M4A转换工具"
    echo ""
    echo "用法:"
    echo "  $0 <输入文件> [输出文件] [音频质量]"
    echo "  $0 -b <输入目录> [输出目录] [音频质量]"
    echo ""
    echo "参数:"
    echo "  输入文件/目录    要转换的MP4文件或包含MP4文件的目录"
    echo "  输出文件/目录    输出的M4A文件或目录（可选）"
    echo "  音频质量         音频比特率，如128k, 192k, 256k等（默认：128k）"
    echo ""
    echo "选项:"
    echo "  -b, --batch     批量转换模式"
    echo "  -h, --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 video.mp4                    # 转换单个文件"
    echo "  $0 video.mp4 audio.m4a         # 指定输出文件名"
    echo "  $0 video.mp4 audio.m4a 192k    # 指定音频质量"
    echo "  $0 -b ./videos ./audios         # 批量转换"
}

# 转换单个文件
convert_single_file() {
    local input_file="$1"
    local output_file="$2"
    local quality="${3:-128k}"
    
    # 检查输入文件是否存在
    if [[ ! -f "$input_file" ]]; then
        echo -e "${RED}错误：输入文件 '$input_file' 不存在${NC}"
        return 1
    fi
    
    # 如果没有指定输出文件，则自动生成
    if [[ -z "$output_file" ]]; then
        output_file="${input_file%.*}.m4a"
    fi
    
    echo -e "${YELLOW}开始转换: $(basename "$input_file") -> $(basename "$output_file")${NC}"
    echo -e "${YELLOW}音频质量: $quality${NC}"
    
    # 执行转换
    if ffmpeg -i "$input_file" -vn -acodec aac -ab "$quality" -y "$output_file" 2>/dev/null; then
        echo -e "${GREEN}转换成功！输出文件: $output_file${NC}"
        return 0
    else
        echo -e "${RED}转换失败！${NC}"
        return 1
    fi
}

# 批量转换
batch_convert() {
    local input_dir="$1"
    local output_dir="$2"
    local quality="${3:-128k}"
    
    # 检查输入目录是否存在
    if [[ ! -d "$input_dir" ]]; then
        echo -e "${RED}错误：输入目录 '$input_dir' 不存在${NC}"
        return 1
    fi
    
    # 设置输出目录
    if [[ -z "$output_dir" ]]; then
        output_dir="$input_dir"
    else
        mkdir -p "$output_dir"
    fi
    
    # 查找所有MP4文件
    local mp4_files=($(find "$input_dir" -maxdepth 1 -name "*.mp4" -o -name "*.MP4"))
    
    if [[ ${#mp4_files[@]} -eq 0 ]]; then
        echo -e "${YELLOW}在目录 '$input_dir' 中没有找到MP4文件${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}找到 ${#mp4_files[@]} 个MP4文件${NC}"
    
    local success_count=0
    for mp4_file in "${mp4_files[@]}"; do
        local filename=$(basename "$mp4_file")
        local output_file="$output_dir/${filename%.*}.m4a"
        
        if convert_single_file "$mp4_file" "$output_file" "$quality"; then
            ((success_count++))
        fi
        echo ""
    done
    
    echo -e "${GREEN}批量转换完成！成功转换 $success_count/${#mp4_files[@]} 个文件${NC}"
}

# 主函数
main() {
    # 检查ffmpeg
    check_ffmpeg
    
    # 解析参数
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        -b|--batch)
            if [[ $# -lt 2 ]]; then
                echo -e "${RED}错误：批量模式需要指定输入目录${NC}"
                show_help
                exit 1
            fi
            batch_convert "$2" "$3" "$4"
            ;;
        "")
            echo -e "${RED}错误：请指定输入文件${NC}"
            show_help
            exit 1
            ;;
        *)
            convert_single_file "$1" "$2" "$3"
            ;;
    esac
}

# 运行主函数
main "$@" 