#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频转M4A转换脚本
使用ffmpeg将视频文件（MP4, MOV等）转换为M4A音频文件
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_ffmpeg():
    """检查系统是否安装了ffmpeg"""
    # 首先检查当前目录下的ffmpeg
    local_ffmpeg = Path('./ffmpeg')
    if local_ffmpeg.exists():
        try:
            subprocess.run([str(local_ffmpeg.absolute()), '-version'], 
                          stdout=subprocess.DEVNULL, 
                          stderr=subprocess.DEVNULL, 
                          check=True)
            return str(local_ffmpeg.absolute())
        except (subprocess.CalledProcessError, FileNotFoundError):
            pass
    
    # 然后检查系统路径中的ffmpeg
    try:
        subprocess.run(['ffmpeg', '-version'], 
                      stdout=subprocess.DEVNULL, 
                      stderr=subprocess.DEVNULL, 
                      check=True)
        return 'ffmpeg'
    except (subprocess.CalledProcessError, FileNotFoundError):
        return None

def convert_video_to_m4a(input_file, output_file=None, quality='128k', ffmpeg_path='ffmpeg'):
    """
    将视频文件转换为M4A文件
    
    Args:
        input_file (str): 输入的视频文件路径（支持MP4, MOV等格式）
        output_file (str): 输出的M4A文件路径，如果为None则自动生成
        quality (str): 音频质量，默认128k
        ffmpeg_path (str): ffmpeg可执行文件路径
    """
    input_path = Path(input_file)
    
    # 检查输入文件是否存在
    if not input_path.exists():
        print(f"错误：输入文件 '{input_file}' 不存在")
        return False
    
    # 如果没有指定输出文件，则自动生成
    if output_file is None:
        output_file = input_path.with_suffix('.m4a')
    
    output_path = Path(output_file)
    
    # 构建ffmpeg命令
    cmd = [
        ffmpeg_path,
        '-i', str(input_path),           # 输入文件
        '-vn',                           # 不包含视频流
        '-acodec', 'aac',               # 使用AAC编码器
        '-ab', quality,                  # 音频比特率
        '-y',                           # 覆盖输出文件
        str(output_path)                # 输出文件
    ]
    
    try:
        print(f"开始转换: {input_path.name} -> {output_path.name}")
        print(f"音频质量: {quality}")
        
        # 执行转换
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"转换成功！输出文件: {output_path}")
            return str(output_path)
        else:
            print(f"转换失败！错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"转换过程中发生错误: {e}")
        return False

def batch_convert(input_dir, output_dir=None, quality='128k', ffmpeg_path='ffmpeg', split_parts=1):
    """
    批量转换目录中的所有视频文件
    
    Args:
        input_dir (str): 输入目录
        output_dir (str): 输出目录，如果为None则使用输入目录
        quality (str): 音频质量
        ffmpeg_path (str): ffmpeg可执行文件路径
        split_parts (int): 分割的份数
    """
    input_path = Path(input_dir)
    
    if not input_path.exists() or not input_path.is_dir():
        print(f"错误：输入目录 '{input_dir}' 不存在或不是目录")
        return
    
    # 查找所有支持的视频文件
    video_files = []
    for pattern in ['*.mp4', '*.MP4', '*.mov', '*.MOV', '*.avi', '*.AVI', '*.mkv', '*.MKV']:
        video_files.extend(list(input_path.glob(pattern)))
    
    if not video_files:
        print(f"在目录 '{input_dir}' 中没有找到支持的视频文件")
        return
    
    # 设置输出目录
    if output_dir is None:
        output_path = input_path
    else:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"找到 {len(video_files)} 个视频文件")
    
    success_count = 0
    for video_file in video_files:
        output_file = output_path / video_file.with_suffix('.m4a').name
        # 调用convert_video_to_m4a，并获取返回的m4a文件路径
        output_m4a_file = convert_video_to_m4a(video_file, output_file, quality, ffmpeg_path)
        
        # 如果转换成功且指定了分割份数，则进行分割
        # 在批量转换模式下，如果指定了输出目录，分割后的文件也应该放在该输出目录中
        if output_m4a_file and split_parts > 1:
            split_m4a_file(output_m4a_file, split_parts, quality, output_dir=output_path, ffmpeg_path=ffmpeg_path)
            success_count += 1 # 只有当转换和分割都成功才算成功？还是只算转换成功？我们在这里只计算转换成功的文件数

    print(f"\n批量转换完成！成功转换 {success_count}/{len(video_files)} 个文件")

def get_audio_duration(audio_file, ffmpeg_path='ffmpeg'):
    """
    获取音频文件的时长（秒）
    
    Args:
        audio_file (str): 音频文件路径
        ffmpeg_path (str): ffmpeg可执行文件路径
        
    Returns:
        float: 音频时长（秒），如果获取失败则返回 None
    """
    cmd = [
        ffmpeg_path,
        '-i', str(Path(audio_file))
    ]
    
    try:
        # ffmpeg -i 命令的输出通常在 stderr
        print(f"执行ffmpeg命令获取时长: {' '.join(cmd)}") # 添加调试输出
        result = subprocess.run(cmd, capture_output=True, text=True)
        output = result.stderr
        print(f"ffmpeg标准输出:\n{result.stdout}") # 添加调试输出
        print(f"ffmpeg标准错误:\n{result.stderr}") # 添加调试输出
        
        # 从输出中查找时长信息，格式通常是 Duration: HH:MM:SS.ms
        duration_line = next((line for line in output.splitlines() if 'Duration:' in line), None)
        
        if duration_line:
            # 提取 HH:MM:SS.ms 部分
            duration_str = duration_line.split('Duration:')[1].split(',')[0].strip()
            
            # 解析 HH:MM:SS.ms 为秒数
            h, m, s = map(float, duration_str.split(':'))
            total_seconds = h * 3600 + m * 60 + s
            return total_seconds
            
    except subprocess.CalledProcessError as e:
        print(f"执行ffmpeg获取时长命令失败: {' '.join(e.cmd)}")
        print(f"ffmpeg标准输出:\n{e.stdout}")
        print(f"ffmpeg标准错误:\n{e.stderr}")
        return None
    except Exception as e:
        print(f"获取音频时长过程中发生未知错误: {e}")
        return None

def split_m4a_file(audio_file, num_parts, quality, output_dir=None, ffmpeg_path='ffmpeg'):
    """
    将M4A文件分割成指定份数
    
    Args:
        audio_file (str): 输入的M4A文件路径
        num_parts (int): 分割的份数
        quality (str): 输出音频的质量
        output_dir (str): 输出目录，如果为None则使用输入文件所在目录
        ffmpeg_path (str): ffmpeg可执行文件路径
    """
    audio_path = Path(audio_file)
    
    if not audio_path.exists():
        print(f"错误：输入文件 '{audio_file}' 不存在")
        return False
        
    if num_parts <= 1:
        print("分割份数必须大于1")
        return False

    duration = get_audio_duration(audio_file, ffmpeg_path)
    if duration is None:
        print(f"无法获取文件时长，无法分割: {audio_file}")
        return False
        
    segment_duration = duration / num_parts
    
    input_dir = audio_path.parent
    base_name = audio_path.stem
    suffix = audio_path.suffix
    
    # 设置输出目录
    if output_dir is None:
        output_path = input_dir
    else:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
    print(f"开始分割文件 '{audio_path.name}' 成 {num_parts} 份")
    
    success_count = 0
    for i in range(num_parts):
        start_time = i * segment_duration
        # For the last segment, calculate duration based on remaining time to ensure it covers till the end
        if i == num_parts - 1:
            current_segment_duration = duration - start_time
        else:
            current_segment_duration = segment_duration
            
        output_filename = f"{base_name}_part{i+1}{suffix}"
        output_file = output_path / output_filename
        
        cmd = [
            ffmpeg_path,
            '-ss', str(start_time),  # 起始时间
            '-i', str(audio_path),    # 输入文件
            '-acodec', 'aac',       # 音频编码器
            '-ab', quality,          # 音频比特率
            '-t', str(current_segment_duration), # 持续时长
            '-y',                   # 覆盖输出文件
            str(output_file)        # 输出文件
        ]
        
        try:
            print(f"  分割第 {i+1}/{num_parts} 份: {output_file.name} (起始: {start_time:.2f}s, 时长: {current_segment_duration:.2f}s)")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"  第 {i+1} 份分割成功")
                success_count += 1
            else:
                print(f"  第 {i+1} 份分割失败！错误信息: {result.stderr}")
                
        except Exception as e:
            print(f"  分割第 {i+1} 份时发生错误: {e}")
            
    print(f"\n分割完成！成功分割 {success_count}/{num_parts} 份")
    return success_count == num_parts

def main():
    parser = argparse.ArgumentParser(description='视频转M4A转换工具')
    parser.add_argument('input', help='输入文件或目录路径')
    parser.add_argument('-o', '--output', help='输出文件或目录路径')
    parser.add_argument('-q', '--quality', default='128k', 
                       help='音频质量 (默认: 128k)')
    parser.add_argument('-b', '--batch', action='store_true',
                       help='批量转换模式（转换目录中的所有视频文件）')
    parser.add_argument('--split_parts', type=int, default=1,
                       help='将输出音频分割成指定的份数 (默认: 1, 不分割)')
    
    args = parser.parse_args()
    
    # 检查ffmpeg是否安装
    ffmpeg_path = check_ffmpeg()
    if not ffmpeg_path:
        print("错误：系统中未找到ffmpeg工具")
        print("请先安装ffmpeg：")
        print("  macOS: brew install ffmpeg")
        print("  Ubuntu: sudo apt install ffmpeg")
        print("  Windows: 从 https://ffmpeg.org/download.html 下载")
        sys.exit(1)
    
    print(f"使用ffmpeg: {ffmpeg_path}")
    
    if args.batch:
        # 批量转换模式
        batch_convert(args.input, args.output, args.quality, ffmpeg_path, args.split_parts)
    else:
        # 单文件转换模式
        output_m4a_file = convert_video_to_m4a(args.input, args.output, args.quality, ffmpeg_path)
        if output_m4a_file and args.split_parts > 1:
            # 如果指定了分割份数且转换成功，则进行分割
            # 如果用户指定了输出目录，则分割后的文件放在输出目录中
            split_output_dir = Path(args.output).parent if args.output and Path(args.output).is_file() else args.output
            split_m4a_file(output_m4a_file, args.split_parts, args.quality, output_dir=split_output_dir, ffmpeg_path=ffmpeg_path)

if __name__ == '__main__':
    main() 