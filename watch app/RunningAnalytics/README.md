# 跑步分析应用

这是一个iOS应用，用于读取和分析用户的健身数据，特别是跑步记录。

## 功能特点

- 从HealthKit读取用户跑步数据
- 展示跑步记录列表
- 显示详细跑步数据（距离、时间、配速、卡路里等）
- 提供统计分析（总计、平均值、最佳记录）
- 展示跑步趋势图表

## 技术栈

- Swift 5
- SwiftUI
- HealthKit
- Charts (iOS 16+)

## 安装要求

- iOS 16.0+ (图表功能需要iOS 16+)
- Xcode 14.0+

## 如何使用

1. 打开应用并授权访问健康数据
2. 在"跑步记录"标签中查看所有跑步记录
3. 点击记录查看详细信息
4. 在"统计分析"标签中查看整体数据统计和趋势
5. 使用"我的"标签管理设置和刷新数据

## 注意事项

- 应用需要访问HealthKit才能获取跑步数据
- 图表功能仅在iOS 16或更高版本可用
- 确保在健康应用中已经有跑步锻炼数据

## 隐私

该应用仅读取用户的健身数据用于展示和分析，不会将数据发送到任何外部服务器或第三方。 