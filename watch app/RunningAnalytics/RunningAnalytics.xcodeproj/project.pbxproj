// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		83FB2A1A2A1B3C7D00DB8D14 /* RunningAnalyticsApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83FB2A192A1B3C7D00DB8D14 /* RunningAnalyticsApp.swift */; };
		83FB2A1C2A1B3C7D00DB8D14 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83FB2A1B2A1B3C7D00DB8D14 /* ContentView.swift */; };
		83FB2A1E2A1B3C7E00DB8D14 /* Assets.xcassets in Sources */ = {isa = PBXBuildFile; fileRef = 83FB2A1D2A1B3C7E00DB8D14 /* Assets.xcassets */; };
		83FB2A212A1B3C7E00DB8D14 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 83FB2A202A1B3C7E00DB8D14 /* Preview Assets.xcassets */; };
		83FB2A292A1B3CA700DB8D14 /* Frameworks in Frameworks */ = {isa = PBXBuildFile; fileRef = 83FB2A282A1B3CA700DB8D14 /* Frameworks */; };
		83FB2A2B2A1B3CC500DB8D14 /* HealthStore.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83FB2A2A2A1B3CC500DB8D14 /* HealthStore.swift */; };
		83FB2A2D2A1B3CDF00DB8D14 /* RunningWorkout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83FB2A2C2A1B3CDF00DB8D14 /* RunningWorkout.swift */; };
		83FB2A2F2A1B3CEC00DB8D14 /* RunningStats.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83FB2A2E2A1B3CEC00DB8D14 /* RunningStats.swift */; };
		83FB2A312A1B3CF800DB8D14 /* WorkoutListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83FB2A302A1B3CF800DB8D14 /* WorkoutListView.swift */; };
		83FB2A332A1B3D0400DB8D14 /* StatsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83FB2A322A1B3D0400DB8D14 /* StatsView.swift */; };
		83FB2A352A1B3D0F00DB8D14 /* ProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83FB2A342A1B3D0F00DB8D14 /* ProfileView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		83FB2A162A1B3C7D00DB8D14 /* RunningAnalytics.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = RunningAnalytics.app; sourceTree = BUILT_PRODUCTS_DIR; };
		83FB2A192A1B3C7D00DB8D14 /* RunningAnalyticsApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunningAnalyticsApp.swift; sourceTree = "<group>"; };
		83FB2A1B2A1B3C7D00DB8D14 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		83FB2A1D2A1B3C7E00DB8D14 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		83FB2A202A1B3C7E00DB8D14 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		83FB2A272A1B3CA700DB8D14 /* RunningAnalytics.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunningAnalytics.entitlements; sourceTree = "<group>"; };
		83FB2A2A2A1B3CC500DB8D14 /* HealthStore.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthStore.swift; sourceTree = "<group>"; };
		83FB2A2C2A1B3CDF00DB8D14 /* RunningWorkout.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunningWorkout.swift; sourceTree = "<group>"; };
		83FB2A2E2A1B3CEC00DB8D14 /* RunningStats.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunningStats.swift; sourceTree = "<group>"; };
		83FB2A302A1B3CF800DB8D14 /* WorkoutListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WorkoutListView.swift; sourceTree = "<group>"; };
		83FB2A322A1B3D0400DB8D14 /* StatsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StatsView.swift; sourceTree = "<group>"; };
		83FB2A342A1B3D0F00DB8D14 /* ProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileView.swift; sourceTree = "<group>"; };
		83FB2A362A1B3D1A00DB8D14 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		83FB2A132A1B3C7D00DB8D14 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				83FB2A292A1B3CA700DB8D14 /* Frameworks in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		83FB2A0D2A1B3C7D00DB8D14 = {
			isa = PBXGroup;
			children = (
				83FB2A182A1B3C7D00DB8D14 /* RunningAnalytics */,
				83FB2A172A1B3C7D00DB8D14 /* Products */,
				83FB2A282A1B3CA700DB8D14 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		83FB2A172A1B3C7D00DB8D14 /* Products */ = {
			isa = PBXGroup;
			children = (
				83FB2A162A1B3C7D00DB8D14 /* RunningAnalytics.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		83FB2A182A1B3C7D00DB8D14 /* RunningAnalytics */ = {
			isa = PBXGroup;
			children = (
				83FB2A362A1B3D1A00DB8D14 /* Info.plist */,
				83FB2A272A1B3CA700DB8D14 /* RunningAnalytics.entitlements */,
				83FB2A192A1B3C7D00DB8D14 /* RunningAnalyticsApp.swift */,
				83FB2A1B2A1B3C7D00DB8D14 /* ContentView.swift */,
				83FB2A372A1B3D2E00DB8D14 /* Views */,
				83FB2A382A1B3D3400DB8D14 /* Models */,
				83FB2A392A1B3D3A00DB8D14 /* Services */,
				83FB2A1D2A1B3C7E00DB8D14 /* Assets.xcassets */,
				83FB2A1F2A1B3C7E00DB8D14 /* Preview Content */,
			);
			path = RunningAnalytics;
			sourceTree = "<group>";
		};
		83FB2A1F2A1B3C7E00DB8D14 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				83FB2A202A1B3C7E00DB8D14 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		83FB2A282A1B3CA700DB8D14 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		83FB2A372A1B3D2E00DB8D14 /* Views */ = {
			isa = PBXGroup;
			children = (
				83FB2A302A1B3CF800DB8D14 /* WorkoutListView.swift */,
				83FB2A322A1B3D0400DB8D14 /* StatsView.swift */,
				83FB2A342A1B3D0F00DB8D14 /* ProfileView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		83FB2A382A1B3D3400DB8D14 /* Models */ = {
			isa = PBXGroup;
			children = (
				83FB2A2C2A1B3CDF00DB8D14 /* RunningWorkout.swift */,
				83FB2A2E2A1B3CEC00DB8D14 /* RunningStats.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		83FB2A392A1B3D3A00DB8D14 /* Services */ = {
			isa = PBXGroup;
			children = (
				83FB2A2A2A1B3CC500DB8D14 /* HealthStore.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		83FB2A152A1B3C7D00DB8D14 /* RunningAnalytics */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 83FB2A242A1B3C7E00DB8D14 /* Build configuration list for PBXNativeTarget "RunningAnalytics" */;
			buildPhases = (
				83FB2A122A1B3C7D00DB8D14 /* Sources */,
				83FB2A132A1B3C7D00DB8D14 /* Frameworks */,
				83FB2A142A1B3C7D00DB8D14 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RunningAnalytics;
			productName = RunningAnalytics;
			productReference = 83FB2A162A1B3C7D00DB8D14 /* RunningAnalytics.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83FB2A0E2A1B3C7D00DB8D14 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1430;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					83FB2A152A1B3C7D00DB8D14 = {
						CreatedOnToolsVersion = 14.3;
						SystemCapabilities = {
							com.apple.HealthKit = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = 83FB2A112A1B3C7D00DB8D14 /* Build configuration list for PBXProject "RunningAnalytics" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83FB2A0D2A1B3C7D00DB8D14;
			productRefGroup = 83FB2A172A1B3C7D00DB8D14 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				83FB2A152A1B3C7D00DB8D14 /* RunningAnalytics */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		83FB2A142A1B3C7D00DB8D14 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				83FB2A212A1B3C7E00DB8D14 /* Preview Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		83FB2A122A1B3C7D00DB8D14 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				83FB2A1C2A1B3C7D00DB8D14 /* ContentView.swift in Sources */,
				83FB2A2B2A1B3CC500DB8D14 /* HealthStore.swift in Sources */,
				83FB2A332A1B3D0400DB8D14 /* StatsView.swift in Sources */,
				83FB2A2D2A1B3CDF00DB8D14 /* RunningWorkout.swift in Sources */,
				83FB2A352A1B3D0F00DB8D14 /* ProfileView.swift in Sources */,
				83FB2A1A2A1B3C7D00DB8D14 /* RunningAnalyticsApp.swift in Sources */,
				83FB2A2F2A1B3CEC00DB8D14 /* RunningStats.swift in Sources */,
				83FB2A312A1B3CF800DB8D14 /* WorkoutListView.swift in Sources */,
				83FB2A1E2A1B3C7E00DB8D14 /* Assets.xcassets in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		83FB2A222A1B3C7E00DB8D14 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 659SPK2J9S;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		83FB2A232A1B3C7E00DB8D14 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 659SPK2J9S;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		83FB2A252A1B3C7E00DB8D14 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = RunningAnalytics/RunningAnalytics.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"RunningAnalytics/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = RunningAnalytics/Info.plist;
				INFOPLIST_KEY_NSHealthShareUsageDescription = "此应用需要访问您的健康数据以显示您的跑步记录和分析结果";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "此应用需要访问您的健康数据以显示您的跑步记录和分析结果";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.RunningAnalytics;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		83FB2A262A1B3C7E00DB8D14 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = RunningAnalytics/RunningAnalytics.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"RunningAnalytics/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = RunningAnalytics/Info.plist;
				INFOPLIST_KEY_NSHealthShareUsageDescription = "此应用需要访问您的健康数据以显示您的跑步记录和分析结果";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "此应用需要访问您的健康数据以显示您的跑步记录和分析结果";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.RunningAnalytics;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		83FB2A112A1B3C7D00DB8D14 /* Build configuration list for PBXProject "RunningAnalytics" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83FB2A222A1B3C7E00DB8D14 /* Debug */,
				83FB2A232A1B3C7E00DB8D14 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83FB2A242A1B3C7E00DB8D14 /* Build configuration list for PBXNativeTarget "RunningAnalytics" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83FB2A252A1B3C7E00DB8D14 /* Debug */,
				83FB2A262A1B3C7E00DB8D14 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83FB2A0E2A1B3C7D00DB8D14 /* Project object */;
}
