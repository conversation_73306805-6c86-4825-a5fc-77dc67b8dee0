import Foundation
import Combine

// 一个简单的服务类，用于AI分析功能
public class AIService: ObservableObject {
    // API设置
    @Published public var apiEndpoint: String = UserDefaults.standard.string(forKey: "AIApiEndpoint") ?? ""
    @Published public var apiKey: String = UserDefaults.standard.string(forKey: "AIApiKey") ?? ""
    @Published public var modelName: String = UserDefaults.standard.string(forKey: "AIModelName") ?? "gpt-3.5-turbo"
    
    // 分析状态
    @Published public var isAnalyzing: Bool = false
    @Published public var analysisResult: String = ""
    @Published public var error: String? = nil
    
    public init() {}
    
    // 保存设置
    public func saveSettings() {
        UserDefaults.standard.set(apiEndpoint, forKey: "AIApiEndpoint")
        UserDefaults.standard.set(apiKey, forKey: "AIApiKey")
        UserDefaults.standard.set(modelName, forKey: "AIModelName") 
    }
    
    // 检查是否配置完成
    public var isConfigured: Bool {
        return !apiEndpoint.isEmpty && !apiKey.isEmpty && !modelName.isEmpty
    }
    
    // AI分析单次跑步
    public func analyzeWorkout(_ workout: RunningWorkout, recentWorkouts: [RunningWorkout]) {
        guard isConfigured else {
            self.error = "请先在设置中配置AI API"
            return
        }
        
        self.isAnalyzing = true
        self.error = nil
        
        // 准备提交给AI的数据
        let workoutData = prepareWorkoutData(workout, recentWorkouts: recentWorkouts)
        
        // 创建AI请求
        var request = URLRequest(url: URL(string: apiEndpoint)!)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        
        // 准备请求体
        let prompt = createPrompt(workout: workout, recentWorkouts: recentWorkouts)
        let requestBody: [String: Any] = [
            "model": modelName,
            "messages": [
                ["role": "system", "content": "你是一位专业的跑步教练和运动分析师，擅长分析跑步数据并提供专业的改进建议。"],
                ["role": "user", "content": prompt]
            ],
            "temperature": 0.7
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody, options: [])
        } catch {
            self.error = "准备请求数据失败: \(error.localizedDescription)"
            self.isAnalyzing = false
            return
        }
        
        // 发送请求
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                if let error = error {
                    self.error = "请求失败: \(error.localizedDescription)"
                    self.isAnalyzing = false
                    return
                }
                
                guard let data = data else {
                    self.error = "没有收到数据"
                    self.isAnalyzing = false
                    return
                }
                
                do {
                    if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let choices = json["choices"] as? [[String: Any]],
                       let firstChoice = choices.first,
                       let message = firstChoice["message"] as? [String: Any],
                       let content = message["content"] as? String {
                        self.analysisResult = content
                    } else {
                        // 尝试获取错误信息
                        if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                           let error = json["error"] as? [String: Any],
                           let message = error["message"] as? String {
                            self.error = "API错误: \(message)"
                        } else {
                            self.error = "无法解析API响应"
                        }
                    }
                } catch {
                    self.error = "解析响应失败: \(error.localizedDescription)"
                }
                
                self.isAnalyzing = false
            }
        }.resume()
    }
    
    // 准备发送给AI的数据
    private func prepareWorkoutData(_ workout: RunningWorkout, recentWorkouts: [RunningWorkout]) -> [String: Any] {
        // 当前跑步的基本信息
        var data: [String: Any] = [
            "date": formatDate(workout.startDate),
            "duration": workout.formattedDuration,
            "distance": workout.formattedDistance,
            "pace": workout.formattedPace,
            "heartRate": Int(workout.averageHeartRate),
            "calories": Int(workout.energyBurned),
            "type": workout.isOutdoor ? "户外跑步" : "室内跑步"
        ]
        
        // 分段数据
        if !workout.splitData.isEmpty {
            var splits: [[String: Any]] = []
            for split in workout.splitData {
                splits.append([
                    "kilometer": split.kilometer,
                    "pace": split.formattedPace,
                    "heartRate": Int(split.averageHeartRate),
                    "cadence": Int(split.averageCadence)
                ])
            }
            data["splits"] = splits
        }
        
        // 心率区间数据
        if !workout.heartRateData.isEmpty {
            let zones = workout.heartRateZoneDurations(age: 30) // 使用默认年龄
            data["heartRateZones"] = zones.mapValues { Int($0) }
        }
        
        // 最近的跑步数据趋势
        var recentStats: [String: Any] = [:]
        
        // 按周统计
        let calendar = Calendar.current
        var weeklyStats: [String: Double] = [:]
        
        for rWorkout in recentWorkouts {
            let weekOfYear = calendar.component(.weekOfYear, from: rWorkout.startDate)
            let year = calendar.component(.year, from: rWorkout.startDate)
            let weekKey = "\(year)-\(weekOfYear)"
            
            let distance = (rWorkout.customDistance ?? rWorkout.distance) / 1000 // 转换为公里
            weeklyStats[weekKey] = (weeklyStats[weekKey] ?? 0) + distance
        }
        
        // 排序周数据
        let sortedWeeks = weeklyStats.keys.sorted()
        var weeklyDistance: [[String: Any]] = []
        
        for week in sortedWeeks {
            if let distance = weeklyStats[week] {
                weeklyDistance.append([
                    "week": week,
                    "distance": String(format: "%.1f", distance)
                ])
            }
        }
        
        recentStats["weeklyDistance"] = weeklyDistance
        
        // 计算最近跑步的平均配速变化
        if recentWorkouts.count > 1 {
            let recentPaces = recentWorkouts.prefix(5).map { $0.pace }
            if !recentPaces.isEmpty {
                let avgPace = recentPaces.reduce(0, +) / Double(recentPaces.count)
                recentStats["recentAveragePace"] = String(format: "%.2f", avgPace)
            }
        }
        
        data["recentStats"] = recentStats
        
        return data
    }
    
    // 创建AI分析提示
    private func createPrompt(workout: RunningWorkout, recentWorkouts: [RunningWorkout]) -> String {
        let workoutData = prepareWorkoutData(workout, recentWorkouts: recentWorkouts)
        
        // 将数据转换为JSON字符串
        guard let jsonData = try? JSONSerialization.data(withJSONObject: workoutData, options: .prettyPrinted),
              let jsonString = String(data: jsonData, encoding: .utf8) else {
            return "数据准备错误"
        }
        
        // 创建提示词
        return """
        请对我的这次跑步进行专业分析并提供改进建议。以下是我的跑步数据：
        
        ```
        \(jsonString)
        ```
        
        请从以下几个方面分析：
        
        1. 本次跑步的整体表现评估，针对配速、心率、步频和距离的分析
        2. 分段配速分析，指出优势和需要改进的地方
        3. 心率区间分析，是否在有效的训练区间
        4. 基于最近几周的跑量和配速变化趋势，评估我的训练进展
        5. 给出针对性的训练建议，如何改进我的跑步表现
        6. 基于我的数据，建议下一阶段的训练计划和目标
        
        请用专业但易懂的语言回答，给出具体可行的建议。回答格式为markdown，使用适当的标题、列表和强调，方便阅读。
        """
    }
    
    // 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return formatter.string(from: date)
    }
} 