import Foundation
import HealthKit
import CoreLocation
import MapKit

class HealthStore: ObservableObject {
    private var healthStore: HKHealthStore?
    @Published var runningWorkouts: [RunningWorkout] = []
    @Published var isAuthorized = false
    @Published var userAge: Int = 30 // 默认年龄
    private var customDistances: [String: Double] = [:] // 存储自定义距离，key是workout的id
    
    init() {
        if HKHealthStore.isHealthDataAvailable() {
            healthStore = HKHealthStore()
        }
    }
    
    func requestAuthorization() {
        guard let healthStore = healthStore else { return }
        
        let readTypes: Set<HKObjectType> = [
            HKObjectType.workoutType(),
            HKObjectType.quantityType(forIdentifier: .distanceWalkingRunning)!,
            HKObjectType.quantityType(forIdentifier: .activeEnergyBurned)!,
            HKObjectType.quantityType(forIdentifier: .heartRate)!,
            HKObjectType.quantityType(forIdentifier: .runningSpeed)!,
            HKObjectType.quantityType(forIdentifier: .runningPower)!,
            HKObjectType.quantityType(forIdentifier: .runningStrideLength)!,
            HKObjectType.quantityType(forIdentifier: .runningVerticalOscillation)!,
            HKObjectType.quantityType(forIdentifier: .runningGroundContactTime)!,
            HKSeriesType.workoutRoute(),
            HKObjectType.characteristicType(forIdentifier: .dateOfBirth)!
        ]
        
        healthStore.requestAuthorization(toShare: nil, read: readTypes) { success, error in
            DispatchQueue.main.async {
                if success {
                    self.isAuthorized = true
                    self.fetchUserAge()
                    self.fetchRunningWorkouts()
                } else {
                    print("授权请求失败: \(String(describing: error))")
                }
            }
        }
    }
    
    func fetchUserAge() {
        guard let healthStore = healthStore else { return }
        
        do {
            let birthdayComponents = try healthStore.dateOfBirthComponents()
            let today = Calendar.current.dateComponents([.year], from: Date())
            
            if let birthYear = birthdayComponents.year, let currentYear = today.year {
                self.userAge = currentYear - birthYear
            }
        } catch {
            print("获取用户年龄失败: \(error)")
        }
    }
    
    func fetchRunningWorkouts() {
        guard let healthStore = healthStore, isAuthorized else { return }
        
        // 加载自定义距离数据
        loadCustomDistances()
        
        let workoutPredicate = HKQuery.predicateForWorkouts(with: .running)
        
        let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
        
        let query = HKSampleQuery(
            sampleType: HKObjectType.workoutType(),
            predicate: workoutPredicate,
            limit: 50,
            sortDescriptors: [sortDescriptor]
        ) { [weak self] (_, samples, error) in
            guard let self = self, let workouts = samples as? [HKWorkout], error == nil else {
                print("获取跑步锻炼数据失败: \(String(describing: error))")
                return
            }
            
            var runningWorkouts: [RunningWorkout] = []
            
            let group = DispatchGroup()
            
        for workout in workouts {
            group.enter()
            
            self.processWorkout(workout) { runningWorkout in
                if let workout = runningWorkout {
                    // 添加自定义距离
                    let workoutWithCustomDistance = RunningWorkout(
                        id: workout.id,
                        startDate: workout.startDate,
                        endDate: workout.endDate,
                        duration: workout.duration,
                        distance: workout.distance,
                        customDistance: self.customDistances[workout.id],  // 确保正确
                        energyBurned: workout.energyBurned,
                        averageHeartRate: workout.averageHeartRate,
                        workoutType: workout.workoutType,
                        locations: workout.locations,
                        routeData: workout.routeData,
                        splitData: workout.splitData,
                        heartRateData: workout.heartRateData,
                        stepData: workout.stepData,
                        elevationData: workout.elevationData
                    )
                    runningWorkouts.append(workoutWithCustomDistance)
                }
                group.leave()
            }
        }
            
            group.notify(queue: .main) {
                self.runningWorkouts = runningWorkouts.sorted(by: { $0.startDate > $1.startDate })
            }
        }
        
        healthStore.execute(query)
    }
    
    private func processWorkout(_ workout: HKWorkout, completion: @escaping (RunningWorkout?) -> Void) {
        // 修复未使用变量警告：直接检查 self.healthStore 是否为 nil
        guard self.healthStore != nil else {
            completion(nil)
            return
        }
        
        let group = DispatchGroup()
        
        // 初始变量
        var heartRateData: [HeartRateData] = []
        var stepData: [StepData] = []
        var elevationData: [ElevationData] = []
        var locations: [CLLocation] = []
        var workoutRoute: HKWorkoutRoute?
        var splitData: [SplitData] = []
        
        // 获取心率数据
        group.enter()
        self.fetchHeartRateData(for: workout) { heartRate, heartRateSamples in
            heartRateData = heartRateSamples
            group.leave()
        }
        
        // 获取步频和步幅数据
        group.enter()
        self.fetchStepData(for: workout) { cadenceSamples in
            stepData = cadenceSamples
            group.leave()
        }
        
        // 获取路线和海拔数据（仅户外跑步）
        if workout.workoutActivityType == .running {
            group.enter()
            self.fetchRouteData(for: workout) { route, routeLocations, elevationSamples in
                workoutRoute = route
                locations = routeLocations
                elevationData = elevationSamples
                group.leave()
            }
        } else {
            // 室内跑步没有路线和海拔数据
            elevationData = []
            locations = []
        }
        
        // 计算分段数据
        group.enter()
        self.calculateSplitData(for: workout, heartRateData: heartRateData, stepData: stepData) { splits in
            splitData = splits
            group.leave()
        }
        
        group.notify(queue: .main) {
            // 修改原始初始化以包含 customDistance
            let runningWorkout = RunningWorkout(
                id: workout.uuid.uuidString,
                startDate: workout.startDate,
                endDate: workout.endDate,
                duration: workout.duration,
                distance: workout.totalDistance?.doubleValue(for: .meter()) ?? 0,
                customDistance: self.customDistances[workout.uuid.uuidString], // 添加自定义距离参数
                energyBurned: workout.totalEnergyBurned?.doubleValue(for: .kilocalorie()) ?? 0,
                averageHeartRate: self.calculateAverageHeartRate(heartRateData),
                workoutType: workout.workoutActivityType,
                locations: locations.isEmpty ? nil : locations,
                routeData: workoutRoute,
                splitData: splitData,
                heartRateData: heartRateData,
                stepData: stepData,
                elevationData: elevationData.isEmpty ? nil : elevationData
            )
            
            completion(runningWorkout) // 返回修改后的 workout
        }
    }

    // MARK: - Private Helper Methods for Data Fetching

    private func fetchHeartRateData(for workout: HKWorkout, completion: @escaping (Double, [HeartRateData]) -> Void) {
        guard let healthStore = healthStore else {
            completion(0, [])
            return
        }
        
        let heartRateType = HKQuantityType.quantityType(forIdentifier: .heartRate)!
        
        let predicate = HKQuery.predicateForSamples(
            withStart: workout.startDate,
            end: workout.endDate,
            options: .strictStartDate
        )
        
        // 获取平均心率
        let statsQuery = HKStatisticsQuery(
            quantityType: heartRateType,
            quantitySamplePredicate: predicate,
            options: .discreteAverage
        ) { _, statistics, _ in
            var avgHeartRate = 0.0
            
            if let quantity = statistics?.averageQuantity() {
                avgHeartRate = quantity.doubleValue(for: HKUnit.count().unitDivided(by: HKUnit.minute()))
            }
            
            // 获取详细心率样本
            let sampleQuery = HKSampleQuery(
                sampleType: heartRateType,
                predicate: predicate,
                limit: HKObjectQueryNoLimit,
                sortDescriptors: [NSSortDescriptor(key: HKSampleSortIdentifierStartDate, ascending: true)]
            ) { _, samples, error in
                var heartRateSamples: [HeartRateData] = []
                
                if let samples = samples as? [HKQuantitySample], error == nil {
                    for sample in samples {
                        let value = sample.quantity.doubleValue(for: HKUnit.count().unitDivided(by: HKUnit.minute()))
                        let data = HeartRateData(
                            timestamp: sample.startDate,
                            value: value
                        )
                        heartRateSamples.append(data)
                    }
                }
                
                DispatchQueue.main.async {
                    completion(avgHeartRate, heartRateSamples)
                }
            }
            
            healthStore.execute(sampleQuery)
        }
        
        healthStore.execute(statsQuery)
    }
    
    private func fetchStepData(for workout: HKWorkout, completion: @escaping ([StepData]) -> Void) {
        guard let healthStore = healthStore else {
            completion([])
            return
        }
        
        let cadenceType = HKQuantityType.quantityType(forIdentifier: .runningStrideLength)! // 注意：这里用的是步幅来估算步频
        
        let predicate = HKQuery.predicateForSamples(
            withStart: workout.startDate,
            end: workout.endDate,
            options: .strictStartDate
        )
        
        let query = HKSampleQuery(
            sampleType: cadenceType,
            predicate: predicate,
            limit: HKObjectQueryNoLimit,
            sortDescriptors: [NSSortDescriptor(key: HKSampleSortIdentifierStartDate, ascending: true)]
        ) { _, samples, error in
            var stepSamples: [StepData] = []
            
            if let samples = samples as? [HKQuantitySample], error == nil {
                for sample in samples {
                    let strideLength = sample.quantity.doubleValue(for: HKUnit.meter())
                    
                    // 从步幅计算步频（大致估算, 需要速度数据才能更准确）
                    // 假设一个恒定速度来估算，这并不准确
                    let estimatedSpeed = 1.66 // 假设 6 分钟/公里的配速 (10 km/h = 1.66 m/s)
                    let cadence = strideLength > 0 ? (estimatedSpeed / strideLength) * 60 * 2 : 0 // 步/分钟 (乘以2因为一步包含左右脚)
                    
                    let data = StepData(
                        timestamp: sample.startDate,
                        cadence: cadence,
                        strideLength: strideLength
                    )
                    stepSamples.append(data)
                }
            }
            
            DispatchQueue.main.async {
                completion(stepSamples)
            }
        }
        
        healthStore.execute(query)
    }
    
    private func fetchRouteData(for workout: HKWorkout, completion: @escaping (HKWorkoutRoute?, [CLLocation], [ElevationData]) -> Void) {
        guard let healthStore = healthStore else {
            completion(nil, [], [])
            return
        }
        
        // 查询与锻炼相关的路线
        let routeType = HKSeriesType.workoutRoute()
        let predicate = HKQuery.predicateForObjects(from: workout)
        
        let routeQuery = HKAnchoredObjectQuery(
            type: routeType,
            predicate: predicate,
            anchor: nil,
            limit: HKObjectQueryNoLimit
        ) { _, samples, _, _, error in
            guard let routes = samples as? [HKWorkoutRoute], let route = routes.first, error == nil else {
                // 没有路线数据，可能是室内跑步或数据不可用
                completion(nil, [], [])
                return
            }
            
            // 获取路线中的位置数据
            var allLocations: [CLLocation] = []
            var elevationSamples: [ElevationData] = []
            
            let locationsQuery = HKWorkoutRouteQuery(route: route) { query, locations, done, error in
                guard error == nil else {
                    // 处理查询错误
                    print("获取路线位置数据失败: \(String(describing: error))")
                    if done { // 确保即使出错也调用 completion
                         DispatchQueue.main.async { completion(route, allLocations, elevationSamples) }
                    }
                    return
                }

                if let locations = locations {
                    allLocations.append(contentsOf: locations)
                    
                    // 从位置数据中提取海拔信息
                    for location in locations {
                        if location.verticalAccuracy >= 0 { // 检查海拔精度是否有效
                            let elevation = ElevationData(
                                timestamp: location.timestamp,
                                elevation: location.altitude
                            )
                            elevationSamples.append(elevation)
                        }
                    }
                }
                
                if done {
                    DispatchQueue.main.async {
                        completion(route, allLocations, elevationSamples)
                    }
                }
            }
            
            healthStore.execute(locationsQuery)
        }
        
        healthStore.execute(routeQuery)
    }
    
    private func calculateSplitData(for workout: HKWorkout, heartRateData: [HeartRateData], stepData: [StepData], completion: @escaping ([SplitData]) -> Void) {
        // 使用实际距离或自定义距离
        let distanceToUse = customDistances[workout.uuid.uuidString] ?? workout.totalDistance?.doubleValue(for: .meter()) ?? 0
        
        guard distanceToUse > 0 else {
            completion([])
            return
        }
        
        let totalDistance = distanceToUse
        let kilometers = Int(floor(totalDistance / 1000)) // 使用 floor 避免不足整公里的情况
        
        var splits: [SplitData] = []
        
        // 如果距离少于1公里，不计算分段
        if kilometers < 1 {
            completion([])
            return
        }
        
        // 为每公里创建一个分段
        // TODO: 需要更精确的方法来确定每公里的时间和相关数据点，目前是简化估算
        for km in 1...kilometers {
            let startDistance = Double(km - 1) * 1000
            let endDistance = Double(km) * 1000
            
            // 简化估算：按比例分配时间
            let progress = startDistance / totalDistance
            let endProgress = endDistance / totalDistance
            
            let startTimeOffset = workout.duration * progress
            let endTimeOffset = workout.duration * endProgress
            
            let startTime = workout.startDate.addingTimeInterval(startTimeOffset)
            let endTime = workout.startDate.addingTimeInterval(endTimeOffset)
            
            // 查找该时间范围内的心率数据
            let relevantHeartRate = heartRateData.filter { $0.timestamp >= startTime && $0.timestamp <= endTime }
            let avgHeartRate = relevantHeartRate.isEmpty ? 0 : relevantHeartRate.reduce(0.0) { $0 + $1.value } / Double(relevantHeartRate.count)
            
            // 查找该时间范围内的步频数据
            let relevantStepData = stepData.filter { $0.timestamp >= startTime && $0.timestamp <= endTime }
            let avgCadence = relevantStepData.isEmpty ? 0 : relevantStepData.reduce(0.0) { $0 + $1.cadence } / Double(relevantStepData.count)
            
            // 计算该公里段的配速
            let segmentDuration = endTime.timeIntervalSince(startTime)
            let paceMins = segmentDuration > 0 ? (segmentDuration / 60) : 0 // 分钟/公里 (假设每段距离是1公里)
            
            splits.append(SplitData(
                kilometer: km,
                pace: paceMins,
                averageHeartRate: avgHeartRate,
                averageCadence: avgCadence,
                startTime: startTime,
                endTime: endTime
            ))
        }
        
        completion(splits)
    }
    
    private func calculateAverageHeartRate(_ heartRateData: [HeartRateData]) -> Double {
        guard !heartRateData.isEmpty else { return 0 }
        let sum = heartRateData.reduce(0.0) { $0 + $1.value }
        return sum / Double(heartRateData.count)
    }

    // MARK: - Custom Distance Management

    // 保存自定义距离
    func saveCustomDistance(_ distance: Double, forWorkoutId id: String) {
        customDistances[id] = distance
        saveCustomDistances()
        fetchRunningWorkouts()  // 重新加载数据以更新UI
    }
    
    // 加载自定义距离数据
    private func loadCustomDistances() {
        if let data = UserDefaults.standard.dictionary(forKey: "CustomDistances") as? [String: Double] {
            customDistances = data
        }
    }
    
    // 保存自定义距离数据到UserDefaults
    private func saveCustomDistances() {
        UserDefaults.standard.set(customDistances, forKey: "CustomDistances")
    }

    // 为指定的workout准备数据，解决详情显示问题
    func prepareWorkoutData(for selectedWorkout: RunningWorkout) {
        // 找到原始的HKWorkout
        guard let healthStore = healthStore, isAuthorized else { return }
        
        let workoutPredicate = HKQuery.predicateForWorkouts(with: .running)
        let sortDescriptor = NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)
        
        let query = HKSampleQuery(
            sampleType: HKObjectType.workoutType(),
            predicate: workoutPredicate,
            limit: 50, // 获取足够多的运动记录以确保包含我们需要的
            sortDescriptors: [sortDescriptor]
        ) { [weak self] (_, samples, error) in
            guard let self = self, let workouts = samples as? [HKWorkout], error == nil else {
                print("获取跑步锻炼数据失败: \(String(describing: error))")
                return
            }
            
            // 查找与selectedWorkout匹配的HKWorkout
            if let matchingWorkout = workouts.first(where: { $0.uuid.uuidString == selectedWorkout.id }) {
                // 为这个特定的workout重新处理数据
                self.processWorkout(matchingWorkout) { runningWorkout in
                    if let processedWorkout = runningWorkout {
                        // 使用自定义距离创建完整的workout
                        let updatedWorkout = RunningWorkout(
                            id: processedWorkout.id,
                            startDate: processedWorkout.startDate,
                            endDate: processedWorkout.endDate,
                            duration: processedWorkout.duration,
                            distance: processedWorkout.distance,
                            customDistance: self.customDistances[processedWorkout.id], 
                            energyBurned: processedWorkout.energyBurned,
                            averageHeartRate: processedWorkout.averageHeartRate,
                            workoutType: processedWorkout.workoutType,
                            locations: processedWorkout.locations,
                            routeData: processedWorkout.routeData,
                            splitData: processedWorkout.splitData,
                            heartRateData: processedWorkout.heartRateData,
                            stepData: processedWorkout.stepData,
                            elevationData: processedWorkout.elevationData
                        )
                        
                        // 更新runningWorkouts数组中的相应条目
                        DispatchQueue.main.async {
                            if let index = self.runningWorkouts.firstIndex(where: { $0.id == selectedWorkout.id }) {
                                self.runningWorkouts[index] = updatedWorkout
                            }
                        }
                    }
                }
            }
        }
        
        healthStore.execute(query)
    }
}
