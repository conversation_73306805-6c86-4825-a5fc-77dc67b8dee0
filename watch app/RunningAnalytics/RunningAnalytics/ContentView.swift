import SwiftUI

struct ContentView: View {
    @EnvironmentObject var healthStore: HealthStore
    
    var body: some View {
        TabView {
            WorkoutListView()
                .tabItem {
                    Label("跑步记录", systemImage: "figure.run")
                }
            
            StatsView()
                .tabItem {
                    Label("统计分析", systemImage: "chart.bar.fill")
                }
            
            ProfileView()
                .tabItem {
                    Label("我的", systemImage: "person.fill")
                }
        }
        .accentColor(.green)
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .environmentObject(HealthStore())
    }
} 