import Foundation

struct RunningStats {
    let workouts: [RunningWorkout]
    
    // 总计统计
    var totalWorkouts: Int {
        return workouts.count
    }
    
    var totalDistance: Double {
        return workouts.reduce(0) { $0 + $1.distance }
    }
    
    var totalDuration: TimeInterval {
        return workouts.reduce(0) { $0 + $1.duration }
    }
    
    var totalCalories: Double {
        return workouts.reduce(0) { $0 + $1.energyBurned }
    }
    
    // 平均值统计
    var averageDistance: Double {
        guard totalWorkouts > 0 else { return 0 }
        return totalDistance / Double(totalWorkouts)
    }
    
    var averageDuration: TimeInterval {
        guard totalWorkouts > 0 else { return 0 }
        return totalDuration / Double(totalWorkouts)
    }
    
    var averagePace: Double {
        guard totalDistance > 0 else { return 0 }
        return totalDuration / 60 / (totalDistance / 1000)
    }
    
    var averageCalories: Double {
        guard totalWorkouts > 0 else { return 0 }
        return totalCalories / Double(totalWorkouts)
    }
    
    var averageHeartRate: Double {
        guard totalWorkouts > 0 else { return 0 }
        return workouts.reduce(0) { $0 + $1.averageHeartRate } / Double(totalWorkouts)
    }
    
    // 最好成绩
    var fastestPace: Double {
        return workouts.filter { $0.distance > 1000 }.min { $0.pace < $1.pace }?.pace ?? 0
    }
    
    var longestDistance: Double {
        return workouts.max { $0.distance < $1.distance }?.distance ?? 0
    }
    
    // 格式化方法
    var formattedTotalDistance: String {
        if totalDistance >= 1000 {
            return String(format: "%.1f 公里", totalDistance / 1000)
        } else {
            return String(format: "%.0f 米", totalDistance)
        }
    }
    
    var formattedTotalDuration: String {
        let hours = Int(totalDuration) / 3600
        let minutes = (Int(totalDuration) % 3600) / 60
        
        if hours > 0 {
            return String(format: "%d小时%02d分", hours, minutes)
        } else {
            return String(format: "%d分钟", minutes)
        }
    }
    
    var formattedAveragePace: String {
        let minutes = Int(averagePace)
        let seconds = Int((averagePace - Double(minutes)) * 60)
        return String(format: "%d'%02d\"/公里", minutes, seconds)
    }
    
    var formattedFastestPace: String {
        guard fastestPace > 0 else { return "无数据" }
        let minutes = Int(fastestPace)
        let seconds = Int((fastestPace - Double(minutes)) * 60)
        return String(format: "%d'%02d\"/公里", minutes, seconds)
    }
    
    // 获取特定时间段的统计数据
    func statsForPeriod(days: Int) -> RunningStats {
        let calendar = Calendar.current
        let now = Date()
        
        // 对于月份视图使用自然月
        if days == 30 {
            // 获取当前月份的起始日期
            let components = calendar.dateComponents([.year, .month], from: now)
            if let startOfMonth = calendar.date(from: components) {
                let filteredWorkouts = workouts.filter { $0.startDate >= startOfMonth && $0.startDate <= now }
                return RunningStats(workouts: filteredWorkouts)
            }
        }
        
        // 对于周视图或全部视图使用之前的天数逻辑
        let cutoffDate = calendar.date(byAdding: .day, value: -days, to: now)!
        let filteredWorkouts = workouts.filter { $0.startDate >= cutoffDate }
        return RunningStats(workouts: filteredWorkouts)
    }
    
    // 趋势分析
    func getWeeklyDistanceTrend(weeks: Int = 8) -> [(week: String, distance: Double)] {
        var result: [(week: String, distance: Double)] = []
        let calendar = Calendar.current
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM/dd"
        
        // 获取当前周的起始日期（星期一）
        let today = Date()
        let weekdayComponents = calendar.dateComponents([.weekday], from: today)
        // 在日历中，星期日是1，星期一是2，所以我们需要调整
        let daysToSubtract = (weekdayComponents.weekday ?? 1) - calendar.firstWeekday
        let adjustedDaysToSubtract = daysToSubtract <= 0 ? daysToSubtract + 7 : daysToSubtract
        
        guard let currentWeekStart = calendar.date(byAdding: .day, value: -adjustedDaysToSubtract, to: today) else {
            return result
        }
        
        for i in 0..<weeks {
            let startDate = calendar.date(byAdding: .day, value: -7 * i, to: currentWeekStart)!
            let endDate = calendar.date(byAdding: .day, value: 6, to: startDate)!
            
            // 确保结束日期不超过今天
            let adjustedEndDate = endDate > today ? today : endDate
            
            let weekWorkouts = workouts.filter { workout in
                workout.startDate >= startDate && workout.startDate <= adjustedEndDate
            }
            
            let totalDistance = weekWorkouts.reduce(0) { $0 + $1.distance } / 1000 // 转换为公里
            
            let weekLabel = "\(dateFormatter.string(from: startDate))-\(dateFormatter.string(from: adjustedEndDate))"
            result.append((week: weekLabel, distance: totalDistance))
        }
        
        return result.reversed()
    }
}
