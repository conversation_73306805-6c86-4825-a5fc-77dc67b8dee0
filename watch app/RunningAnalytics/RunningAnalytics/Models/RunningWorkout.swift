import Foundation
import MapKit
import HealthKit

struct RunningWorkout: Identifiable {
    let id: String
    let startDate: Date
    let endDate: Date
    let duration: TimeInterval
    let distance: Double
    let customDistance: Double? // 用户自定义距离，优先于HealthKit数据
    let energyBurned: Double
    let averageHeartRate: Double
    
    // 新增字段
    let workoutType: HKWorkoutActivityType // 区分室内和户外跑步
    let locations: [CLLocation]? // 跑步路线坐标点
    let routeData: HKWorkoutRoute? // 原始路线数据
    let splitData: [SplitData] // 每公里分段数据
    let heartRateData: [HeartRateData] // 心率数据点
    let stepData: [StepData] // 步频数据点
    let elevationData: [ElevationData]? // 海拔数据点（仅户外跑步有）
    
    // 计算属性
    var pace: Double {
        let displayDistance = customDistance ?? distance
        guard displayDistance > 0 else { return 0 }
        return duration / 60 / (displayDistance / 1000) // 分钟/公里
    }
    
    var averageSpeed: Double {
        let displayDistance = customDistance ?? distance
        guard duration > 0 else { return 0 }
        return displayDistance / (duration / 3600) // 米/小时
    }
    
    var isOutdoor: Bool {
        // 户外跑步的类型是 .running 并且有路线数据
        return workoutType == .running && locations != nil && !(locations?.isEmpty ?? true)
    }
    
    var isIndoor: Bool {
        // 室内跑步（跑步机）的类型是 .running 但没有路线数据
        return workoutType == .running && (locations == nil || locations?.isEmpty ?? true)
    }
    
    // 心率区间时间（秒）
    func heartRateZoneDurations(age: Int) -> [String: TimeInterval] {
        // 最大心率估算: 220 - 年龄
        let maxHeartRate = 220 - age
        
        // 心率区间定义（基于最大心率的百分比）
        let zones: [(String, Double, Double)] = [
            ("轻松", 0.5, 0.6),      // 50-60%
            ("热身", 0.6, 0.7),      // 60-70%
            ("燃脂", 0.7, 0.8),      // 70-80%
            ("有氧", 0.8, 0.9),      // 80-90%
            ("无氧", 0.9, 1.0)       // 90-100%
        ]
        
        var result: [String: TimeInterval] = [:]
        
        // 初始化所有区间为0
        for (name, _, _) in zones {
            result[name] = 0
        }
        
        // 如果没有心率数据或只有一个点，返回空结果
        guard heartRateData.count > 1 else {
            return result
        }
        
        // 计算每个区间的持续时间
        for i in 0..<(heartRateData.count - 1) {
            let hr = heartRateData[i].value
            let hrPercent = Double(hr) / Double(maxHeartRate)
            
            // 时间差（秒）
            let timeDiff = heartRateData[i+1].timestamp.timeIntervalSince(heartRateData[i].timestamp)
            
            // 确定当前心率属于哪个区间
            for (name, min, max) in zones {
                if hrPercent >= min && hrPercent < max {
                    result[name] = (result[name] ?? 0) + timeDiff
                    break
                }
            }
        }
        
        return result
    }
    
    // 日期格式化
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: startDate)
    }
    
    var formattedDuration: String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        let seconds = Int(duration) % 60
        
        if hours > 0 {
            return String(format: "%d小时%02d分%02d秒", hours, minutes, seconds)
        } else {
            return String(format: "%d分%02d秒", minutes, seconds)
        }
    }
    
    var formattedDistance: String {
        let displayDistance = customDistance ?? distance
        if displayDistance >= 1000 {
            return String(format: "%.2f 公里", displayDistance / 1000)
        } else {
            return String(format: "%.0f 米", displayDistance)
        }
    }
    
    var formattedPace: String {
        let minutes = Int(pace)
        let seconds = Int((pace - Double(minutes)) * 60)
        return String(format: "%d'%02d\"/公里", minutes, seconds)
    }
    
    // 将路线点转换为地图显示区域
    var routeRegion: MKCoordinateRegion? {
        guard let locations = locations, !locations.isEmpty else { return nil }
        
        var minLat = locations[0].coordinate.latitude
        var maxLat = locations[0].coordinate.latitude
        var minLon = locations[0].coordinate.longitude
        var maxLon = locations[0].coordinate.longitude
        
        for location in locations {
            minLat = min(minLat, location.coordinate.latitude)
            maxLat = max(maxLat, location.coordinate.latitude)
            minLon = min(minLon, location.coordinate.longitude)
            maxLon = max(maxLon, location.coordinate.longitude)
        }
        
        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )
        
        let span = MKCoordinateSpan(
            latitudeDelta: (maxLat - minLat) * 1.5,
            longitudeDelta: (maxLon - minLon) * 1.5
        )
        
        return MKCoordinateRegion(center: center, span: span)
    }
}

// 每公里分段数据
struct SplitData: Identifiable {
    let id = UUID()
    let kilometer: Int // 第几公里
    let pace: Double // 配速（分钟/公里）
    let averageHeartRate: Double // 平均心率
    let averageCadence: Double // 平均步频
    let startTime: Date
    let endTime: Date
    
    var duration: TimeInterval {
        return endTime.timeIntervalSince(startTime)
    }
    
    var formattedPace: String {
        let minutes = Int(pace)
        let seconds = Int((pace - Double(minutes)) * 60)
        return String(format: "%d'%02d\"", minutes, seconds)
    }
}

// 心率数据点
struct HeartRateData: Identifiable {
    let id = UUID()
    let timestamp: Date
    let value: Double // 心率（次/分钟）
}

// 步频数据点
struct StepData: Identifiable {
    let id = UUID()
    let timestamp: Date
    let cadence: Double // 步频（步/分钟）
    let strideLength: Double? // 步幅（米）
}

// 海拔数据点
struct ElevationData: Identifiable {
    let id = UUID()
    let timestamp: Date
    let elevation: Double // 海拔（米）
}
