import SwiftUI
import Charts

struct StatsView: View {
    @EnvironmentObject var healthStore: HealthStore
    @State private var selectedPeriod = 2 // 0: 周, 1: 月, 2: 全部
    
    private var periodOptions = ["本周", "本月", "全部"]
    private var daysPeriod: [Int] = [7, 30, 365*5]
    
    private var stats: RunningStats {
        let period = selectedPeriod < periodOptions.count ? daysPeriod[selectedPeriod] : 365*5
        return RunningStats(workouts: healthStore.runningWorkouts).statsForPeriod(days: period)
    }
    
    private var weeklyTrend: [(week: String, distance: Double)] {
        return stats.getWeeklyDistanceTrend()
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 时间段选择器
                    Picker("时间段", selection: $selectedPeriod) {
                        ForEach(0..<periodOptions.count, id: \.self) { index in
                            Text(periodOptions[index]).tag(index)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .padding(.horizontal)
                    
                    // 数据摘要
                    VStack(spacing: 15) {
                        StatSummaryView(
                            title: "总跑步次数",
                            value: "\(stats.totalWorkouts)次",
                            icon: "figure.run",
                            color: .green
                        )
                        
                        StatSummaryView(
                            title: "总距离",
                            value: stats.formattedTotalDistance,
                            icon: "arrow.left.and.right",
                            color: .blue
                        )
                        
                        StatSummaryView(
                            title: "总时长",
                            value: stats.formattedTotalDuration,
                            icon: "clock",
                            color: .orange
                        )
                        
                        StatSummaryView(
                            title: "总消耗",
                            value: String(format: "%.0f 千卡", stats.totalCalories),
                            icon: "flame.fill",
                            color: .red
                        )
                    }
                    .padding(.horizontal)
                    
                    Divider()
                        .padding(.horizontal)
                    
                    // 平均数据
                    VStack(alignment: .leading, spacing: 5) {
                        Text("平均表现")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        HStack {
                            AverageStatView(
                                title: "平均距离",
                                value: String(format: "%.1f公里", stats.averageDistance / 1000),
                                icon: "arrow.up.and.down"
                            )
                            
                            AverageStatView(
                                title: "平均时长",
                                value: String(format: "%.0f分钟", stats.averageDuration / 60),
                                icon: "clock"
                            )
                            
                            AverageStatView(
                                title: "平均配速",
                                value: stats.formattedAveragePace,
                                icon: "speedometer"
                            )
                        }
                        .padding(.horizontal)
                    }
                    
                    Divider()
                        .padding(.horizontal)
                    
                    // 最佳记录
                    VStack(alignment: .leading, spacing: 5) {
                        Text("最佳记录")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        HStack {
                            BestRecordView(
                                title: "最佳配速",
                                value: stats.formattedFastestPace,
                                icon: "bolt.fill"
                            )
                            
                            BestRecordView(
                                title: "最长距离",
                                value: String(format: "%.1f公里", stats.longestDistance / 1000),
                                icon: "arrow.left.and.right"
                            )
                        }
                        .padding(.horizontal)
                    }
                    
                    Divider()
                        .padding(.horizontal)
                    
                    // 趋势图表
                    VStack(alignment: .leading, spacing: 10) {
                        Text("每周跑步距离")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        if #available(iOS 16.0, *) {
                            ChartView(data: weeklyTrend)
                                .padding(.horizontal)
                        } else {
                            Text("图表需要iOS 16或更高版本")
                                .font(.footnote)
                                .foregroundColor(.gray)
                                .frame(height: 250)
                                .frame(maxWidth: .infinity)
                        }
                    }
                }
                .padding(.vertical)
            }
            .navigationTitle("统计分析")
            .background(Color(UIColor.systemGroupedBackground))
        }
    }
}

struct StatSummaryView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40)
            
            VStack(alignment: .leading) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                
                Text(value)
                    .font(.title3)
                    .fontWeight(.bold)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(UIColor.secondarySystemGroupedBackground))
        .cornerRadius(10)
    }
}

struct AverageStatView: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 5) {
            Image(systemName: icon)
                .foregroundColor(.blue)
            
            Text(value)
                .font(.callout)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(UIColor.secondarySystemGroupedBackground))
        .cornerRadius(10)
    }
}

struct BestRecordView: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.orange)
                .font(.title3)
            
            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text(value)
                    .font(.callout)
                    .fontWeight(.semibold)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(UIColor.secondarySystemGroupedBackground))
        .cornerRadius(10)
    }
}

@available(iOS 16.0, *)
struct ChartView: View {
    let data: [(week: String, distance: Double)]
    
    // 计算Y轴的最大值，更智能地确定范围
    private var yAxisMax: Double {
        let maxDistance = data.map { $0.distance }.max() ?? 5
        // 向上取整到最接近的5公里整数
        return ceil(maxDistance / 5) * 5
    }
    
    var body: some View {
        Chart {
            ForEach(data.indices, id: \.self) { index in
                let item = data[index]
                BarMark(
                    x: .value("周次", index), // 使用索引作为X轴值
                    y: .value("距离(公里)", item.distance)
                )
                .foregroundStyle(Color.green.gradient)
                .annotation(position: .top) {
                    Text(String(format: "%.1f", item.distance))
                        .font(.system(size: 8))
                        .foregroundColor(.gray)
                }
            }
        }
        .chartYScale(domain: 0...yAxisMax)
        .chartXAxis {
            AxisMarks(preset: .aligned, values: .automatic) { value in
                if let index = value.as(Int.self), index >= 0 && index < data.count {
                    // 显示每个X轴标签对应的周期标签
                    AxisValueLabel {
                        Text(data[index].week)
                            .font(.system(size: 8))
                            .lineLimit(1)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .offset(y: 10) // 增加向下的偏移
                }
                AxisGridLine()
                AxisTick()
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading, values: .automatic) { value in
                AxisValueLabel {
                    if let yValue = value.as(Double.self) {
                        Text("\(Int(yValue))")
                            .font(.system(size: 8))
                    }
                }
                AxisGridLine()
            }
        }
        .padding(.top, 10) // 为顶部数值标注留出空间
        .frame(height: 250)
    }
}

struct StatsView_Previews: PreviewProvider {
    static var previews: some View {
        StatsView()
            .environmentObject(HealthStore())
    }
} 