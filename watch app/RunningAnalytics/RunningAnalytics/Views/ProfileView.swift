import SwiftUI
import HealthKit
import SafariServices
import Foundation

struct ProfileView: View {
    @EnvironmentObject var healthStore: HealthStore
    @EnvironmentObject var aiService: AIService
    @State private var showHealthSettings = false
    @State private var showAISettings = false
    
    var body: some View {
        NavigationView {
            List {
                Section {
                    HStack {
                        Image(systemName: "person.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.green)
                        
                        VStack(alignment: .leading, spacing: 5) {
                            Text("跑步爱好者")
                                .font(.title3)
                                .fontWeight(.bold)
                            
                            Text("加油！坚持就是胜利")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                        }
                        .padding(.leading, 10)
                    }
                    .padding(.vertical, 10)
                }
                
                Section(header: Text("健康数据")) {
                    Button(action: {
                        healthStore.requestAuthorization()
                    }) {
                        HStack {
                            Image(systemName: "heart.fill")
                                .foregroundColor(.red)
                            
                            Text("更新健康数据授权")
                            
                            Spacer()
                            
                            Text(healthStore.isAuthorized ? "已授权" : "未授权")
                                .foregroundColor(.gray)
                        }
                    }
                    
                    Button(action: {
                        showHealthSettings = true
                    }) {
                        HStack {
                            Image(systemName: "gear")
                                .foregroundColor(.gray)
                            
                            Text("健康应用设置")
                            
                            Spacer()
                            
                            Image(systemName: "chevron.right")
                                .foregroundColor(.gray)
                                .imageScale(.small)
                        }
                    }
                }
                
                Section(header: Text("AI分析设置")) {
                    Button(action: {
                        showAISettings = true
                    }) {
                        HStack {
                            Image(systemName: "brain")
                                .foregroundColor(.purple)
                            
                            Text("AI分析配置")
                            
                            Spacer()
                            
                            Text(aiService.isConfigured ? "已配置" : "未配置")
                                .foregroundColor(.gray)
                            
                            Image(systemName: "chevron.right")
                                .foregroundColor(.gray)
                                .imageScale(.small)
                        }
                    }
                }
                
                Section(header: Text("应用信息")) {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)
                        
                        Text("应用版本")
                        
                        Spacer()
                        
                        Text("1.0.0")
                            .foregroundColor(.gray)
                    }
                    
                    HStack {
                        Image(systemName: "doc.text")
                            .foregroundColor(.blue)
                        
                        Text("隐私政策")
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                            .imageScale(.small)
                    }
                    
                    HStack {
                        Image(systemName: "questionmark.circle")
                            .foregroundColor(.blue)
                        
                        Text("帮助与反馈")
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.gray)
                            .imageScale(.small)
                    }
                }
                
                Section {
                    Button(action: {
                        healthStore.fetchRunningWorkouts()
                    }) {
                        HStack {
                            Spacer()
                            
                            Text("刷新数据")
                                .foregroundColor(.green)
                            
                            Spacer()
                        }
                    }
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("我的")
            .sheet(isPresented: $showHealthSettings) {
                if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                    SafariView(url: settingsUrl)
                }
            }
            .sheet(isPresented: $showAISettings) {
                AISettingsView()
                    .environmentObject(aiService)
            }
        }
    }
}

struct AISettingsView: View {
    @EnvironmentObject var aiService: AIService
    @Environment(\.presentationMode) var presentationMode
    
    @State private var endpoint: String = ""
    @State private var apiKey: String = ""
    @State private var model: String = ""
    @State private var showAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("OpenAI兼容API设置"), footer: Text("请填写兼容OpenAI的API信息，如Azure OpenAI或其他兼容服务")) {
                    TextField("API地址", text: $endpoint)
                        .autocapitalization(.none)
                        .keyboardType(.URL)
                    
                    SecureField("API密钥", text: $apiKey)
                        .autocapitalization(.none)
                    
                    TextField("模型名称", text: $model)
                        .autocapitalization(.none)
                }
                
                Section(footer: Text("示例API地址: https://api.openai.com/v1/chat/completions\n示例模型名称: gpt-3.5-turbo")) {
                    Button(action: {
                        saveSettings()
                    }) {
                        HStack {
                            Spacer()
                            Text("保存设置")
                                .bold()
                            Spacer()
                        }
                    }
                    .foregroundColor(.green)
                }
            }
            .navigationBarTitle("AI分析设置", displayMode: .inline)
            .navigationBarItems(trailing: Button("关闭") {
                presentationMode.wrappedValue.dismiss()
            })
            .onAppear {
                // 加载当前设置
                self.endpoint = aiService.apiEndpoint
                self.apiKey = aiService.apiKey
                self.model = aiService.modelName
            }
            .alert(isPresented: $showAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
        }
    }
    
    private func saveSettings() {
        if endpoint.isEmpty || apiKey.isEmpty || model.isEmpty {
            alertMessage = "所有字段都必须填写"
            showAlert = true
            return
        }
        
        aiService.apiEndpoint = endpoint
        aiService.apiKey = apiKey
        aiService.modelName = model
        aiService.saveSettings()
        
        alertMessage = "设置已保存"
        showAlert = true
    }
}

struct SafariView: UIViewControllerRepresentable {
    let url: URL
    
    func makeUIViewController(context: Context) -> SFSafariViewController {
        return SFSafariViewController(url: url)
    }
    
    func updateUIViewController(_ uiViewController: SFSafariViewController, context: Context) {}
}

struct ProfileView_Previews: PreviewProvider {
    static var previews: some View {
        ProfileView()
            .environmentObject(HealthStore())
            .environmentObject(AIService())
    }
}
