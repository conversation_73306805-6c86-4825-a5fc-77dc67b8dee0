import SwiftUI
import MapKit
import CoreLocation

struct WorkoutListView: View {
    @EnvironmentObject var healthStore: HealthStore
    @State private var showDetail: Bool = false
    @State private var selectedWorkout: RunningWorkout?

    var body: some View {
        NavigationView {
            ZStack {
                if healthStore.runningWorkouts.isEmpty {
                    VStack {
                        Image(systemName: "figure.run")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)

                        Text("没有找到跑步记录")
                            .font(.headline)
                            .padding()

                        Text("请授权健康应用访问权限")
                            .font(.subheadline)
                            .foregroundColor(.gray)

                        Button(action: {
                            healthStore.requestAuthorization()
                        }) {
                            Text("重试")
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 30)
                                .padding(.vertical, 10)
                                .background(Color.green)
                                .cornerRadius(10)
                        }
                        .padding(.top, 20)
                    }
                } else {
                    List {
                        ForEach(healthStore.runningWorkouts) { workout in
                            WorkoutRowView(workout: workout)
                                .onTapGesture {
                                    selectedWorkout = workout
                                    showDetail = true
                                }
                        }
                    }
                    .refreshable {
                        healthStore.fetchRunningWorkouts()
                    }
                }
            }
            .navigationTitle("跑步记录")
            .fullScreenCover(isPresented: $showDetail) {
                if let workout = selectedWorkout {
                    // 使用NavigationView包装WorkoutDetailView以确保它正确显示
                    NavigationView {
                        WorkoutDetailView(workout: workout)
                            .environmentObject(healthStore)
                            .onAppear {
                                // 确保数据已准备好
                                healthStore.prepareWorkoutData(for: workout)
                            }
                            .navigationBarItems(leading: Button(action: {
                                showDetail = false
                            }) {
                                Text("关闭")
                            })
                    }
                }
            }
        }
    }
}

struct WorkoutRowView: View {
    let workout: RunningWorkout

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(workout.formattedDate)
                    .font(.headline)

                Spacer()
                // Use formattedDistance which considers customDistance
                Text(workout.formattedDistance)
                    .font(.headline)
                    .foregroundColor(.green)
            }

            HStack {
                HStack(spacing: 2) {
                    Image(systemName: "clock")
                        .foregroundColor(.gray)
                    Text(workout.formattedDuration)
                        .font(.subheadline)
                }

                Spacer()

                HStack(spacing: 2) {
                    Image(systemName: "speedometer")
                        .foregroundColor(.gray)
                    // Use formattedPace which considers customDistance
                    Text(workout.formattedPace)
                        .font(.subheadline)
                }

                Spacer()

                HStack(spacing: 2) {
                    Image(systemName: "flame")
                        .foregroundColor(.red)
                    Text(String(format: "%.0f 千卡", workout.energyBurned))
                        .font(.subheadline)
                }
            }
        }
        .padding(.vertical, 8)
    }
}

struct WorkoutDetailView: View {
    let workout: RunningWorkout
    @EnvironmentObject var healthStore: HealthStore
    @EnvironmentObject var aiService: AIService
    @State private var customDistanceText: String = ""
    @State private var showAlert: Bool = false
    @State private var alertMessage: String = ""
    @State private var showAIAnalysis: Bool = false

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 15) {
                // 地图视图（仅室外跑步）优先显示在最上方
                if workout.isOutdoor, let region = workout.routeRegion, let locations = workout.locations {
                    MapView(region: region, locations: locations)
                        .frame(height: 220)
                        .cornerRadius(10)
                        .padding(.horizontal)
                } else {
                    // 非户外跑步时显示的替代视图
                    VStack {
                        Image(systemName: "figure.indoor.cycle")
                            .font(.system(size: 50))
                            .foregroundColor(.green)

                        Text("室内跑步")
                            .font(.headline)
                            .padding(.top, 8)
                    }
                    .frame(height: 150)
                    .frame(maxWidth: .infinity)
                    .background(Color(UIColor.secondarySystemBackground))
                    .cornerRadius(10)
                    .padding(.horizontal)
                }

                // AI分析按钮
                if aiService.isConfigured {
                    Button(action: {
                        // 显示AI分析视图，后续可以实现完整功能
                        showAIAnalysis = true
                        
                        // 设置分析中状态，模拟AI分析过程
                        aiService.isAnalyzing = true
                        
                        // 延迟2秒后显示"虚拟"分析结果
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                            aiService.isAnalyzing = false
                            aiService.analysisResult = "这是一次很棒的跑步！\n\n您的平均配速为\(workout.formattedPace)，距离\(workout.formattedDistance)。继续保持这样的训练强度，您的跑步能力将会不断提高。"
                        }
                    }) {
                        HStack {
                            Image(systemName: "brain")
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            Text("AI教练分析")
                                .font(.headline)
                                .foregroundColor(.white)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.purple)
                        .cornerRadius(10)
                        .padding(.horizontal)
                    }
                }

                // 运动汇总数据区域
                VStack(alignment: .leading) {
                    Text("运动汇总")
                        .font(.headline)
                        .padding(.horizontal)

                    WorkoutSummaryView(workout: workout)
                        .padding(.horizontal)
                }

                Divider()
                    .padding(.horizontal)

                // 每公里分段数据
                if !workout.splitData.isEmpty {
                    VStack(alignment: .leading) {
                        Text("分段数据")
                            .font(.headline)
                            .padding(.horizontal)

                        SplitDataListView(splits: workout.splitData)
                            .padding(.horizontal)
                    }

                    Divider()
                        .padding(.horizontal)
                }

                // 配速曲线图
                if !workout.splitData.isEmpty {
                    VStack(alignment: .leading) {
                        Text("配速变化")
                            .font(.headline)
                            .padding(.horizontal)

                        PaceChartView(splits: workout.splitData)
                            .frame(height: 200)
                            .cornerRadius(10)
                            .padding(.horizontal)
                    }

                    Divider()
                        .padding(.horizontal)
                }

                // 心率图表
                if !workout.heartRateData.isEmpty {
                    VStack(alignment: .leading) {
                        Text("心率变化")
                            .font(.headline)
                            .padding(.horizontal)

                        HeartRateChartView(heartRateData: workout.heartRateData)
                            .frame(height: 200)
                            .cornerRadius(10)
                            .padding(.horizontal)
                    }

                    // 心率区间
                    EnhancedHeartRateZoneView(workout: workout, userAge: healthStore.userAge)
                        .padding(.horizontal)

                    Divider()
                        .padding(.horizontal)
                }

                // 步频和步幅图表
                if !workout.stepData.isEmpty {
                    VStack(alignment: .leading) {
                        Text("步频变化")
                            .font(.headline)
                            .padding(.horizontal)

                        CadenceChartView(stepData: workout.stepData)
                            .frame(height: 200)
                            .cornerRadius(10)
                            .padding(.horizontal)
                    }

                    VStack(alignment: .leading) {
                        Text("步幅变化")
                            .font(.headline)
                            .padding(.horizontal)

                        StrideLengthChartView(stepData: workout.stepData)
                            .frame(height: 200)
                            .cornerRadius(10)
                            .padding(.horizontal)
                    }

                    Divider()
                        .padding(.horizontal)
                }

                // 海拔图表（仅户外跑步）
                if let elevationData = workout.elevationData, !elevationData.isEmpty {
                    VStack(alignment: .leading) {
                        Text("海拔变化")
                            .font(.headline)
                            .padding(.horizontal)

                        ElevationChartView(elevationData: elevationData)
                            .frame(height: 200)
                            .cornerRadius(10)
                            .padding(.horizontal)
                    }
                     Divider()
                        .padding(.horizontal)
                }

                // 自定义距离区域 (仅室内跑步)
                if workout.isIndoor {
                    VStack(alignment: .leading) {
                        Text("自定义距离")
                            .font(.headline)
                            .padding(.horizontal)

                        HStack {
                            // Input in meters
                            TextField("输入实际跑步距离(米)", text: $customDistanceText)
                                .keyboardType(.decimalPad)
                                .padding()
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(10)

                            Button(action: {
                                // Validate and convert to Double (meters)
                                guard let distanceInMeters = Double(customDistanceText), distanceInMeters >= 0 else {
                                    alertMessage = "请输入有效的距离值 (米)"
                                    showAlert = true
                                    return
                                }

                                healthStore.saveCustomDistance(distanceInMeters, forWorkoutId: workout.id)
                                alertMessage = "已保存自定义距离"
                                showAlert = true
                            }) {
                                Text("保存")
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 20)
                                    .padding(.vertical, 10)
                                    .background(Color.green)
                                    .cornerRadius(10)
                            }
                        }
                        .padding(.horizontal)

                        // Display current custom distance if set
                        if let customDist = workout.customDistance {
                             Text("当前自定义距离: \(String(format: "%.0f", customDist)) 米")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .padding(.horizontal)
                        } else {
                             Text("未设置自定义距离，将使用 HealthKit 数据。")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .padding(.horizontal)
                        }
                    }
                    .padding(.top)
                }
            }
            .padding(.vertical)
            .alert(isPresented: $showAlert) {
                Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
            }
            .sheet(isPresented: $showAIAnalysis) {
                AIAnalysisView(workout: workout)
                    .environmentObject(aiService)
            }
        }
        .navigationTitle(workout.isOutdoor ? "户外跑步" : "室内跑步")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            // Initialize text field with existing custom distance (in meters)
            if let customDist = workout.customDistance {
                customDistanceText = String(format: "%.0f", customDist)
            } else {
                // Optionally clear or set placeholder if no custom distance
                 customDistanceText = ""
            }
        }
    }
}

// AI分析结果视图
struct AIAnalysisView: View {
    let workout: RunningWorkout
    @EnvironmentObject var aiService: AIService
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 15) {
                    if aiService.isAnalyzing {
                        VStack(spacing: 20) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle())
                                .scaleEffect(1.5)
                            
                            Text("AI正在分析您的跑步数据...")
                                .font(.headline)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .padding(.vertical, 100)
                    } else if !aiService.analysisResult.isEmpty {
                        Text(aiService.analysisResult)
                            .padding()
                    } else {
                        VStack(spacing: 20) {
                            Image(systemName: "brain")
                                .font(.system(size: 50))
                                .foregroundColor(.purple)
                            
                            Text("AI分析功能")
                                .font(.title3)
                                .fontWeight(.bold)
                            
                            Text("请在设置中配置API后使用此功能")
                                .font(.body)
                                .multilineTextAlignment(.center)
                                .padding()
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 100)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
            }
            .navigationBarTitle("AI教练分析", displayMode: .inline)
            .navigationBarItems(leading: Button("关闭") {
                // 关闭视图时清除分析结果
                aiService.analysisResult = ""
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

// 运动汇总视图 - 新组件
struct WorkoutSummaryView: View {
    let workout: RunningWorkout

    var body: some View {
        VStack(spacing: 12) {
            // 第一行：主要数据
            HStack(spacing: 0) {
                StatCardView(
                    title: "距离",
                    value: workout.formattedDistance, // Already uses customDistance if available
                    icon: "arrow.left.and.right",
                    color: .green
                )

                StatCardView(
                    title: "时间",
                    value: workout.formattedDuration,
                    icon: "clock",
                    color: .blue
                )

                StatCardView(
                    title: "配速",
                    value: workout.formattedPace, // Already uses customDistance if available
                    icon: "speedometer",
                    color: .orange
                )
            }

            // 第二行：心率、累计爬升
            HStack {
                DetailRowView(
                    title: "平均心率",
                    value: "\(Int(workout.averageHeartRate)) bpm",
                    icon: "heart.fill",
                    color: .red
                )

                if let elevationData = workout.elevationData, !elevationData.isEmpty {
                    let elevations = elevationData.map { $0.elevation }
                    if let maxElevation = elevations.max(), let minElevation = elevations.min() {
                        let elevationGain = max(0, maxElevation - minElevation)
                        DetailRowView(
                            title: "累计爬升",
                            value: String(format: "%.1f 米", elevationGain),
                            icon: "mountain.2.fill",
                            color: .purple
                        )
                    }
                } else if workout.isOutdoor {
                     DetailRowView(
                        title: "累计爬升",
                        value: "无数据",
                        icon: "mountain.2.fill",
                        color: .gray
                    )
                }
            }

            // 第三行：步频、步幅
            if !workout.stepData.isEmpty {
                HStack {
                    let avgCadence = workout.stepData.map { $0.cadence }.reduce(0, +) / Double(workout.stepData.count)
                    DetailRowView(
                        title: "平均步频",
                        value: "\(Int(avgCadence)) 步/分钟",
                        icon: "figure.walk",
                        color: .blue
                    )

                    let validStrideData = workout.stepData.compactMap { $0.strideLength }
                    if !validStrideData.isEmpty {
                        let avgStrideLength = validStrideData.reduce(0, +) / Double(validStrideData.count)
                        DetailRowView(
                            title: "平均步幅",
                            value: String(format: "%.2f 米", avgStrideLength),
                            icon: "ruler",
                            color: .teal
                        )
                    } else {
                         DetailRowView(
                            title: "平均步幅",
                            value: "无数据",
                            icon: "ruler",
                            color: .gray
                        )
                    }
                }
            } else {
                 HStack {
                     DetailRowView(
                        title: "平均步频",
                        value: "无数据",
                        icon: "figure.walk",
                        color: .gray
                    )
                     DetailRowView(
                        title: "平均步幅",
                        value: "无数据",
                        icon: "ruler",
                        color: .gray
                    )
                 }
            }


            // 第四行：运动时间
            HStack {
                DetailRowView(
                    title: "开始时间",
                    value: formatDateTime(workout.startDate),
                    icon: "clock.arrow.circlepath",
                    color: .gray
                )
                 DetailRowView(
                    title: "结束时间",
                    value: formatDateTime(workout.endDate),
                    icon: "clock.arrow.circlepath",
                    color: .gray
                )
            }

            // 注：天气和湿度数据需要额外的API，这里暂未实现
            // 未来可以在这里添加天气和湿度的显示
        }
        .padding()
        .background(Color(UIColor.secondarySystemBackground))
        .cornerRadius(12)
    }

    private func formatDateTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return formatter.string(from: date)
    }
}

struct StatCardView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(color)
                .padding(.bottom, 2)

            Text(value)
                .font(.system(size: 16, weight: .bold))
                .lineLimit(1)
                .minimumScaleFactor(0.7)

            Text(title)
                .font(.system(size: 12))
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .padding(.horizontal, 4)
        .background(Color(UIColor.systemBackground))
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
}

struct DetailRowView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .frame(width: 25, alignment: .center) // Slightly smaller frame
            Text(title)
                .font(.footnote) // Smaller font for title
                .foregroundColor(.gray)
            Spacer()
            Text(value)
                .font(.footnote) // Smaller font for value
                .fontWeight(.medium)
                .multilineTextAlignment(.trailing)
        }
        .padding(.vertical, 4) // Reduced vertical padding
    }
}

// 地图视图
struct MapView: UIViewRepresentable {
    let region: MKCoordinateRegion
    let locations: [CLLocation]

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        mapView.showsUserLocation = false
        mapView.mapType = .standard
        return mapView
    }

    func updateUIView(_ mapView: MKMapView, context: Context) {
        // 清除之前的覆盖层和标注
        mapView.removeOverlays(mapView.overlays)
        mapView.removeAnnotations(mapView.annotations)

        // 确保有足够的坐标点
        guard locations.count > 1 else { return }

        // 创建跑步路线
        let coordinates = locations.map { $0.coordinate }
        let polyline = MKPolyline(coordinates: coordinates, count: coordinates.count)
        mapView.addOverlay(polyline)

        // 添加起点和终点标记
        if let startLocation = locations.first, let endLocation = locations.last {
            let startAnnotation = MKPointAnnotation()
            startAnnotation.coordinate = startLocation.coordinate
            startAnnotation.title = "起点"

            let endAnnotation = MKPointAnnotation()
            endAnnotation.coordinate = endLocation.coordinate
            endAnnotation.title = "终点"

            mapView.addAnnotations([startAnnotation, endAnnotation])
        }

        // 使用更精确的方法设置地图区域
        // 获取坐标的边界
        var minLat = Double.greatestFiniteMagnitude
        var maxLat = -Double.greatestFiniteMagnitude
        var minLon = Double.greatestFiniteMagnitude
        var maxLon = -Double.greatestFiniteMagnitude
        
        for location in locations {
            minLat = min(minLat, location.coordinate.latitude)
            maxLat = max(maxLat, location.coordinate.latitude)
            minLon = min(minLon, location.coordinate.longitude)
            maxLon = max(maxLon, location.coordinate.longitude)
        }
        
        // 计算边界的中心点和范围
        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )
        
        // 添加足够大的边距，考虑到可能的地图投影失真
        let latDelta = (maxLat - minLat) * 1.5 // 增加50%边距
        let lonDelta = (maxLon - minLon) * 1.5
        
        // 确保地图有最小的范围，以便在小范围运动时也能看清
        let minSpan = 0.005 // 约500米
        let span = MKCoordinateSpan(
            latitudeDelta: max(latDelta, minSpan),
            longitudeDelta: max(lonDelta, minSpan)
        )
        
        // 直接使用传入的region作为初始参考点
        mapView.setRegion(MKCoordinateRegion(center: center, span: span), animated: false)
        
        // 确保路线在视图中可见
        let edgePadding = UIEdgeInsets(top: 50, left: 50, bottom: 50, right: 50)
        mapView.setVisibleMapRect(
            polyline.boundingMapRect,
            edgePadding: edgePadding,
            animated: true
        )
    }

    func makeCoordinator() -> Coordinator {
        Coordinator()
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
            if let polyline = overlay as? MKPolyline {
                let renderer = MKPolylineRenderer(polyline: polyline)
                renderer.strokeColor = .systemGreen
                renderer.lineWidth = 4
                return renderer
            }
            return MKOverlayRenderer(overlay: overlay)
        }

        func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
            // 不处理用户位置标注
            if annotation is MKUserLocation {
                return nil
            }

            let identifier = "RunningPin"
            var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)

            if annotationView == nil {
                annotationView = MKMarkerAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                annotationView?.canShowCallout = true
            } else {
                annotationView?.annotation = annotation
            }

            // 区分起点和终点
            if let markerView = annotationView as? MKMarkerAnnotationView {
                if annotation.title == "起点" {
                    markerView.markerTintColor = .systemGreen
                    markerView.glyphImage = UIImage(systemName: "figure.walk")
                } else if annotation.title == "终点" {
                    markerView.markerTintColor = .systemRed
                    markerView.glyphImage = UIImage(systemName: "flag.fill")
                }
            }

            return annotationView
        }
    }
}

// 分段数据视图改为列表形式 - 已优化
struct SplitDataListView: View {
    let splits: [SplitData]

    var body: some View {
        VStack(spacing: 4) {
            // 表头
            HStack {
                Text("公里")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(width: 60, alignment: .center)

                Text("配速")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(width: 80, alignment: .center)

                Text("心率")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(width: 80, alignment: .center)

                Text("步频")
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
                    .frame(width: 80, alignment: .center)
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
            .background(Color(UIColor.secondarySystemBackground))

            Divider()

            // 分段数据行
            ScrollView {
                ForEach(splits) { split in
                    HStack {
                        Text("\(split.kilometer)公里")
                            .font(.system(size: 14))
                            .frame(width: 60, alignment: .center)
                            .bold()

                        Text(split.formattedPace)
                            .font(.system(size: 14))
                            .foregroundColor(.green)
                            .frame(width: 80, alignment: .center)
                            .bold()

                        HStack(spacing: 4) {
                            Image(systemName: "heart.fill")
                                .font(.system(size: 10))
                                .foregroundColor(.red)
                            Text("\(Int(split.averageHeartRate))")
                                .font(.system(size: 14))
                        }
                        .frame(width: 80, alignment: .center)

                        HStack(spacing: 4) {
                            Image(systemName: "figure.walk")
                                .font(.system(size: 10))
                                .foregroundColor(.blue)
                            Text("\(Int(split.averageCadence))")
                                .font(.system(size: 14))
                        }
                        .frame(width: 80, alignment: .center)
                    }
                    .padding(.vertical, 8)
                    .frame(maxWidth: .infinity)
                    .background(split.kilometer % 2 == 0 ? Color(UIColor.secondarySystemBackground).opacity(0.3) : Color.clear)
                }
            }
        }
        .padding(.vertical)
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(10)
        .frame(maxWidth: .infinity) // 确保占据整个屏幕宽度
    }
}

// 优化的心率区间视图
struct EnhancedHeartRateZoneView: View {
    let workout: RunningWorkout
    let userAge: Int

    // Define zones within the struct for clarity
    private let zonesDefinition: [(name: String, color: Color, minPercent: Double, maxPercent: Double)] = [
        ("轻松", .blue, 0.5, 0.6),
        ("热身", .green, 0.6, 0.7),
        ("燃脂", .yellow, 0.7, 0.8),
        ("有氧", .orange, 0.8, 0.9),
        ("无氧", .red, 0.9, 1.0)
    ]

    var body: some View {
        VStack(alignment: .leading) {
            Text("心率区间")
                .font(.headline)
                .padding(.horizontal)

            let zoneDurations = workout.heartRateZoneDurations(age: userAge)
            let maxHR = 220 - userAge
            let totalDuration = getTotalDuration(zoneDurations: zoneDurations)

            VStack(spacing: 12) {
                ForEach(zonesDefinition, id: \.name) { zoneInfo in
                    let duration = zoneDurations[zoneInfo.name] ?? 0
                    let percentage = totalDuration > 0 ? duration / totalDuration : 0
                    let minHR = Int(Double(maxHR) * zoneInfo.minPercent)
                    let maxHR = Int(Double(maxHR) * zoneInfo.maxPercent)

                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(zoneInfo.name)
                                .font(.subheadline)
                                .foregroundColor(zoneInfo.color)
                                .frame(width: 60, alignment: .leading)

                            Text("\(minHR)-\(maxHR) bpm")
                                .font(.caption)
                                .foregroundColor(.gray)

                            Spacer()

                            Text(formatDuration(duration))
                                .font(.caption)
                                .foregroundColor(.gray)

                            Text(String(format: "%.0f%%", percentage * 100))
                                .font(.caption)
                                .bold()
                                .frame(width: 40, alignment: .trailing)
                        }

                        GeometryReader { geometry in
                            let barWidth = max(CGFloat(percentage) * geometry.size.width, 5) // Ensure minimum visible width
                            ZStack(alignment: .leading) {
                                Rectangle()
                                    .frame(height: 12)
                                    .foregroundColor(Color(UIColor.systemGray5))
                                    .cornerRadius(6)

                                Rectangle()
                                    .frame(width: barWidth, height: 12)
                                    .foregroundColor(zoneInfo.color)
                                    .cornerRadius(6)
                            }
                        }
                        .frame(height: 12)
                    }
                }
            }
            .padding()
            .background(Color(UIColor.secondarySystemBackground))
            .cornerRadius(10)
        }
    }

    // Helper function to calculate total duration
    private func getTotalDuration(zoneDurations: [String: TimeInterval]) -> TimeInterval {
        return zoneDurations.values.reduce(0, +)
    }

    private func formatDuration(_ seconds: TimeInterval) -> String {
        let minutes = Int(seconds) / 60
        let seconds = Int(seconds) % 60
        return "\(minutes)分\(seconds)秒"
    }
}

// --- Chart Views Refactoring ---

// Base struct for common chart elements
struct ChartAxisView: View {
    let width: CGFloat
    let height: CGFloat
    let xOffset: CGFloat = 40 // 增加左侧空间，避免标签重叠
    let yOffset: CGFloat // Calculated as height - bottomPadding
    let bottomPadding: CGFloat = 30 // 增加底部空间，避免标签重叠
    let topPadding: CGFloat = 15
    let leftPadding: CGFloat = 40 // 增加左侧空间
    let rightPadding: CGFloat = 10

    var chartHeight: CGFloat { height - topPadding - bottomPadding }
    var chartWidth: CGFloat { width - leftPadding - rightPadding }

    init(width: CGFloat, height: CGFloat) {
        self.width = width
        self.height = height
        self.yOffset = height - bottomPadding
    }

    var body: some View {
        // X轴和Y轴
        Path { path in
            // Y轴
            path.move(to: CGPoint(x: xOffset, y: yOffset))
            path.addLine(to: CGPoint(x: xOffset, y: topPadding))

            // X轴
            path.move(to: CGPoint(x: xOffset, y: yOffset))
            path.addLine(to: CGPoint(x: xOffset + chartWidth, y: yOffset))
        }
        .stroke(Color.gray, lineWidth: 1.5)
    }
}

// Helper to calculate point position
func calculatePoint(index: Int, count: Int, value: Double, minVal: Double, maxVal: Double, chartWidth: CGFloat, chartHeight: CGFloat, xOffset: CGFloat, yOffset: CGFloat) -> CGPoint {
    let xPos = xOffset + chartWidth * CGFloat(index) / CGFloat(max(1, count - 1))
    let valueRange = maxVal - minVal
    let normalizedValue = valueRange > 0 ? (value - minVal) / valueRange : 0.5
    let yPos = yOffset - CGFloat(normalizedValue) * chartHeight
    return CGPoint(x: xPos, y: yPos)
}

func calculateTimePoint(timestamp: Date, minTime: Date, maxTime: Date, value: Double, minVal: Double, maxVal: Double, chartWidth: CGFloat, chartHeight: CGFloat, xOffset: CGFloat, yOffset: CGFloat) -> CGPoint {
    let duration = maxTime.timeIntervalSince(minTime)
    guard duration > 0 else { return CGPoint(x: xOffset, y: yOffset - chartHeight / 2) } // Avoid division by zero

    let timeOffset = timestamp.timeIntervalSince(minTime)
    let xPos = xOffset + (chartWidth * CGFloat(timeOffset / duration))

    let valueRange = maxVal - minVal
    let normalizedValue = valueRange > 0 ? (value - minVal) / valueRange : 0.5
    let yPos = yOffset - CGFloat(normalizedValue) * chartHeight
    return CGPoint(x: xPos, y: yPos)
}

// Reusable Grid and Labels View
struct ChartGridAndLabelsView: View {
    let axis: ChartAxisView
    let xCount: Int
    let yCount: Int
    let xLabelProvider: (Int) -> String
    let yLabelProvider: (Int) -> String
    let yAxisLabel: String
    let xAxisLabel: String

    var body: some View {
        // Draw Y-axis grid lines and labels
        ForEach(0..<yCount, id: \.self) { i in
            let yGrid = axis.yOffset - CGFloat(i) * (axis.chartHeight / CGFloat(yCount - 1))

            // Horizontal grid line
            Path { path in
                path.move(to: CGPoint(x: axis.xOffset, y: yGrid))
                path.addLine(to: CGPoint(x: axis.xOffset + axis.chartWidth, y: yGrid))
            }
            .stroke(Color.gray.opacity(0.3), style: StrokeStyle(lineWidth: 1, dash: [2])) // Dashed lines

            // Y-axis label
            Text(yLabelProvider(i))
                .font(.system(size: 8))
                .foregroundColor(.gray)
                .position(x: axis.xOffset - 20, y: yGrid) // 向左移动标签
        }

        // Draw X-axis grid lines and labels
        let xStep = axis.chartWidth / CGFloat(max(1, xCount - 1))
        ForEach(0..<xCount, id: \.self) { i in
            let xGrid = axis.xOffset + CGFloat(i) * xStep

            // Vertical grid line
            Path { path in
                path.move(to: CGPoint(x: xGrid, y: axis.yOffset - axis.chartHeight))
                path.addLine(to: CGPoint(x: xGrid, y: axis.yOffset))
            }
            .stroke(Color.gray.opacity(0.3), style: StrokeStyle(lineWidth: 1, dash: [2])) // Dashed lines

            // X-axis label
            Text(xLabelProvider(i))
                .font(.system(size: 8))
                .foregroundColor(.gray)
                .position(x: xGrid, y: axis.yOffset + 20) // 向下移动标签
        }


        // Axis Labels
        Text(yAxisLabel)
            .font(.system(size: 10))
            .foregroundColor(.gray)
            .rotationEffect(.degrees(-90))
            .position(x: 10, y: axis.yOffset - axis.chartHeight / 2) // 更靠左的位置

        Text(xAxisLabel)
            .font(.system(size: 10))
            .foregroundColor(.gray)
            .position(x: axis.xOffset + axis.chartWidth / 2, y: axis.height - 5) // 更靠下的位置
    }
}

// 配速图表 - 改进版本，使用弯曲线条
struct PaceChartView: View {
    let splits: [SplitData]

    var body: some View {
        if splits.isEmpty {
            Text("没有分段数据")
                .foregroundColor(.gray)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else {
            GeometryReader { geometry in
                let axis = ChartAxisView(width: geometry.size.width, height: geometry.size.height)
                
                // 根据数据动态计算合适的范围
                let paceValues = splits.map { $0.pace }
                let avgPace = paceValues.reduce(0, +) / Double(paceValues.count)
                let maxDeviation = paceValues.map { abs($0 - avgPace) }.max() ?? 0
                
                // 确保范围至少有一定的最小值，并添加一些边距
                let minPadding = max(avgPace * 0.1, 0.2) // 至少有0.2或10%的边距
                let minPace = max(0, avgPace - max(maxDeviation, minPadding) * 1.2)
                let maxPace = avgPace + max(maxDeviation, minPadding) * 1.2

                ZStack {
                    axis // Draw axis first

                    // Grid lines and labels
                    ChartGridAndLabelsView(
                        axis: axis,
                        xCount: min(splits.count, 10), // 限制标签数量
                        yCount: 5,
                        xLabelProvider: { index in 
                            let adjustedIndex = Int(Double(index) / Double(min(splits.count, 10) - 1) * Double(splits.count - 1))
                            return "\(splits[adjustedIndex].kilometer)" 
                        },
                        yLabelProvider: { index in
                            let paceValue = minPace + (maxPace - minPace) * Double(index) / 4
                            let paceMinutes = Int(paceValue)
                            let paceSeconds = Int((paceValue - Double(paceMinutes)) * 60)
                            return "\(paceMinutes)'\(String(format: "%02d\"", paceSeconds))"
                        },
                        yAxisLabel: "配速",
                        xAxisLabel: "公里"
                    )

                    // 使用CubicCurve绘制平滑配速曲线
                    if splits.count >= 2 {
                        PaceCurve(
                            splits: splits,
                            minPace: minPace,
                            maxPace: maxPace,
                            chartWidth: axis.chartWidth,
                            chartHeight: axis.chartHeight,
                            xOffset: axis.xOffset,
                            yOffset: axis.yOffset
                        )
                        .stroke(Color.green, lineWidth: 2.5)
                    }

                    // Draw Data Points
                    ForEach(0..<splits.count, id: \.self) { index in
                        let point = calculatePoint(
                            index: index, count: splits.count, value: splits[index].pace,
                            minVal: minPace, maxVal: maxPace,
                            chartWidth: axis.chartWidth, chartHeight: axis.chartHeight,
                            xOffset: axis.xOffset, yOffset: axis.yOffset
                        )
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                            .position(point)
                    }
                }
            }
        }
    }
}

// 配速曲线平滑绘制
struct PaceCurve: Shape {
    let splits: [SplitData]
    let minPace: Double
    let maxPace: Double
    let chartWidth: CGFloat
    let chartHeight: CGFloat
    let xOffset: CGFloat
    let yOffset: CGFloat
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        guard splits.count >= 2 else { return path }
        
        // 创建控制点数组
        var points = [CGPoint]()
        for (index, split) in splits.enumerated() {
            let point = calculatePoint(
                index: index, count: splits.count, value: split.pace,
                minVal: minPace, maxVal: maxPace,
                chartWidth: chartWidth, chartHeight: chartHeight,
                xOffset: xOffset, yOffset: yOffset
            )
            points.append(point)
        }
        
        // 绘制曲线
        path.move(to: points[0])
        
        // 使用三次贝塞尔曲线连接点
        for i in 1..<points.count {
            let previous = points[i-1]
            let current = points[i]
            
            // 计算控制点，使曲线更平滑
            let controlPoint1 = CGPoint(
                x: previous.x + (current.x - previous.x) / 3,
                y: previous.y
            )
            let controlPoint2 = CGPoint(
                x: current.x - (current.x - previous.x) / 3,
                y: current.y
            )
            
            path.addCurve(to: current, control1: controlPoint1, control2: controlPoint2)
        }
        
        return path
    }
}

// 心率图表 - 优化版本
struct HeartRateChartView: View {
    let heartRateData: [HeartRateData]

    var body: some View {
        if heartRateData.count < 2 {
            Text("没有足够的心率数据")
                .foregroundColor(.gray)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else {
            GeometryReader { geometry in
                let axis = ChartAxisView(width: geometry.size.width, height: geometry.size.height)
                
                // 智能计算心率范围
                let hrValues = heartRateData.map { $0.value }
                let avgHR = hrValues.reduce(0, +) / Double(hrValues.count)
                let maxDeviation = hrValues.map { abs($0 - avgHR) }.max() ?? 0
                
                // 最小范围为平均值的±15%
                let minRange = avgHR * 0.15
                let rangeToUse = max(maxDeviation * 1.2, minRange)
                
                let minHeartRate = max(0, avgHR - rangeToUse)
                let maxHeartRate = avgHR + rangeToUse
                
                let minTime = heartRateData.first!.timestamp
                let maxTime = heartRateData.last!.timestamp

                ZStack {
                    axis // Draw axis

                    // Grid lines and labels
                    ChartGridAndLabelsView(
                        axis: axis,
                        xCount: 5, // Time intervals
                        yCount: 5,
                        xLabelProvider: { index in
                            let duration = maxTime.timeIntervalSince(minTime)
                            let timeOffset = duration * Double(index) / 4
                            return formatTime(minTime.addingTimeInterval(timeOffset))
                        },
                        yLabelProvider: { index in
                            let hbValue = minHeartRate + (maxHeartRate - minHeartRate) * Double(index) / 4
                            return "\(Int(hbValue))"
                        },
                        yAxisLabel: "bpm",
                        xAxisLabel: "时间"
                    )

                    // Draw Heart Rate Line
                    Path { path in
                        for (index, data) in heartRateData.enumerated() {
                            let point = calculateTimePoint(
                                timestamp: data.timestamp, minTime: minTime, maxTime: maxTime,
                                value: data.value, minVal: minHeartRate, maxVal: maxHeartRate,
                                chartWidth: axis.chartWidth, chartHeight: axis.chartHeight,
                                xOffset: axis.xOffset, yOffset: axis.yOffset
                            )
                            if index == 0 {
                                path.move(to: point)
                            } else {
                                path.addLine(to: point)
                            }
                        }
                    }
                    .stroke(Color.red, lineWidth: 2)
                }
            }
        }
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

// 步频图表 - Refactored
struct CadenceChartView: View {
    let stepData: [StepData]

    var body: some View {
        if stepData.count < 2 {
            Text("没有足够的步频数据")
                .foregroundColor(.gray)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else {
            GeometryReader { geometry in
                let axis = ChartAxisView(width: geometry.size.width, height: geometry.size.height)
                let minCadence = (stepData.map { $0.cadence }.min() ?? 0) * 0.9
                let maxCadence = (stepData.map { $0.cadence }.max() ?? 0) * 1.1
                let minTime = stepData.first!.timestamp
                let maxTime = stepData.last!.timestamp

                ZStack {
                    axis // Draw axis

                    // Grid lines and labels
                    ChartGridAndLabelsView(
                        axis: axis,
                        xCount: 5, // Time intervals
                        yCount: 5,
                        xLabelProvider: { index in
                            let duration = maxTime.timeIntervalSince(minTime)
                            let timeOffset = duration * Double(index) / 4
                            return formatTime(minTime.addingTimeInterval(timeOffset))
                        },
                        yLabelProvider: { index in
                            let cadenceValue = minCadence + (maxCadence - minCadence) * Double(index) / 4
                            return "\(Int(cadenceValue))"
                        },
                        yAxisLabel: "步/分钟",
                        xAxisLabel: "时间"
                    )

                    // Draw Cadence Line
                    Path { path in
                        for (index, data) in stepData.enumerated() {
                            let point = calculateTimePoint(
                                timestamp: data.timestamp, minTime: minTime, maxTime: maxTime,
                                value: data.cadence, minVal: minCadence, maxVal: maxCadence,
                                chartWidth: axis.chartWidth, chartHeight: axis.chartHeight,
                                xOffset: axis.xOffset, yOffset: axis.yOffset
                            )
                            if index == 0 {
                                path.move(to: point)
                            } else {
                                path.addLine(to: point)
                            }
                        }
                    }
                    .stroke(Color.blue, lineWidth: 2)
                }
            }
        }
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

// 步幅图表 - Refactored
struct StrideLengthChartView: View {
    let stepData: [StepData]

    var body: some View {
        let validData = stepData.filter { $0.strideLength != nil && $0.strideLength! > 0 } // Ensure valid stride length

        if validData.count < 2 {
            Text("没有足够的步幅数据")
                .foregroundColor(.gray)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else {
            GeometryReader { geometry in
                let axis = ChartAxisView(width: geometry.size.width, height: geometry.size.height)
                let strideValues = validData.compactMap { $0.strideLength }
                let minStrideLength = (strideValues.min() ?? 0) * 0.9
                let maxStrideLength = (strideValues.max() ?? 0) * 1.1
                let minTime = validData.first!.timestamp
                let maxTime = validData.last!.timestamp

                ZStack {
                    axis // Draw axis

                    // Grid lines and labels
                    ChartGridAndLabelsView(
                        axis: axis,
                        xCount: 5, // Time intervals
                        yCount: 5,
                        xLabelProvider: { index in
                            let duration = maxTime.timeIntervalSince(minTime)
                            let timeOffset = duration * Double(index) / 4
                            return formatTime(minTime.addingTimeInterval(timeOffset))
                        },
                        yLabelProvider: { index in
                            let strideValue = minStrideLength + (maxStrideLength - minStrideLength) * Double(index) / 4
                            return String(format: "%.2f", strideValue)
                        },
                        yAxisLabel: "米",
                        xAxisLabel: "时间"
                    )

                    // Draw Stride Length Line
                    Path { path in
                        for (index, data) in validData.enumerated() {
                             if let strideLength = data.strideLength { // Should always be non-nil here due to filter
                                let point = calculateTimePoint(
                                    timestamp: data.timestamp, minTime: minTime, maxTime: maxTime,
                                    value: strideLength, minVal: minStrideLength, maxVal: maxStrideLength,
                                    chartWidth: axis.chartWidth, chartHeight: axis.chartHeight,
                                    xOffset: axis.xOffset, yOffset: axis.yOffset
                                )
                                if index == 0 {
                                    path.move(to: point)
                                } else {
                                    path.addLine(to: point)
                                }
                            }
                        }
                    }
                    .stroke(Color.teal, lineWidth: 2)
                }
            }
        }
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}


// 海拔图表 - Refactored
struct ElevationChartView: View {
    let elevationData: [ElevationData]

    var body: some View {
        if elevationData.count < 2 {
            Text("没有足够的海拔数据")
                .foregroundColor(.gray)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else {
            GeometryReader { geometry in
                 let axis = ChartAxisView(width: geometry.size.width, height: geometry.size.height)
                 let elevations = elevationData.map { $0.elevation }
                 let minElevation = (elevations.min() ?? 0) * 0.9
                 let maxElevation = (elevations.max() ?? 0) * 1.1
                 let minTime = elevationData.first!.timestamp
                 let maxTime = elevationData.last!.timestamp

                 ZStack {
                     axis // Draw axis

                     // Grid lines and labels
                     ChartGridAndLabelsView(
                         axis: axis,
                         xCount: 5, // Time intervals
                         yCount: 5,
                         xLabelProvider: { index in
                             let duration = maxTime.timeIntervalSince(minTime)
                             let timeOffset = duration * Double(index) / 4
                             return formatTime(minTime.addingTimeInterval(timeOffset))
                         },
                         yLabelProvider: { index in
                             let elevValue = minElevation + (maxElevation - minElevation) * Double(index) / 4
                             return "\(Int(elevValue))m"
                         },
                         yAxisLabel: "海拔(m)",
                         xAxisLabel: "时间"
                     )

                     // Draw Elevation Fill Area
                     Path { path in
                         // Start at bottom-left baseline
                         path.move(to: CGPoint(x: axis.xOffset, y: axis.yOffset))

                         // Draw points along the top edge
                         for data in elevationData {
                             let point = calculateTimePoint(
                                 timestamp: data.timestamp, minTime: minTime, maxTime: maxTime,
                                 value: data.elevation, minVal: minElevation, maxVal: maxElevation,
                                 chartWidth: axis.chartWidth, chartHeight: axis.chartHeight,
                                 xOffset: axis.xOffset, yOffset: axis.yOffset
                             )
                             path.addLine(to: point)
                         }

                         // End at bottom-right baseline and close path
                         let lastX = axis.xOffset + axis.chartWidth
                         path.addLine(to: CGPoint(x: lastX, y: axis.yOffset))
                         path.closeSubpath()
                     }
                     .fill(Color.purple.opacity(0.2))


                     // Draw Elevation Line
                     Path { path in
                         for (index, data) in elevationData.enumerated() {
                             let point = calculateTimePoint(
                                 timestamp: data.timestamp, minTime: minTime, maxTime: maxTime,
                                 value: data.elevation, minVal: minElevation, maxVal: maxElevation,
                                 chartWidth: axis.chartWidth, chartHeight: axis.chartHeight,
                                 xOffset: axis.xOffset, yOffset: axis.yOffset
                             )
                             if index == 0 {
                                 path.move(to: point)
                             } else {
                                 path.addLine(to: point)
                             }
                         }
                     }
                     .stroke(Color.purple, lineWidth: 2)
                 }
            }
        }
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

struct WorkoutListView_Previews: PreviewProvider {
    static var previews: some View {
        // Create mock data for preview
        let mockHealthStore = HealthStore()
        // Add some mock workouts if needed for previewing WorkoutDetailView
        // mockHealthStore.runningWorkouts = [mockWorkout1, mockWorkout2]

        WorkoutListView()
            .environmentObject(mockHealthStore) // Use mock data
    }
}
