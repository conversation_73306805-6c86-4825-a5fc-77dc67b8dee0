#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频压缩工具 - 快速测试脚本
用于验证核心功能是否正常
"""

import subprocess
import sys
import os
from pathlib import Path

def test_python():
    """测试Python环境"""
    print("🔍 测试Python环境...")
    print(f"Python版本: {sys.version}")
    
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7+")
        return False
    
    print("✅ Python环境正常")
    return True

def test_ffmpeg():
    """测试FFmpeg"""
    print("\n🔍 测试FFmpeg...")
    
    # 测试系统FFmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ 系统FFmpeg可用")
            return True
    except:
        pass
    
    # 测试本地FFmpeg
    if Path("ffmpeg.exe").exists():
        try:
            result = subprocess.run(['./ffmpeg.exe', '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ 本地FFmpeg可用")
                return True
        except:
            pass
    
    print("❌ 未找到FFmpeg")
    print("请运行 setup_and_build.py 或手动下载FFmpeg")
    return False

def test_dependencies():
    """测试Python依赖"""
    print("\n🔍 测试Python依赖...")
    
    required_modules = ['tkinter', 'subprocess', 'threading', 're', 'os', 'pathlib']
    optional_modules = ['requests', 'PIL']
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - 缺少必需模块")
            return False
    
    for module in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module} (可选)")
        except ImportError:
            print(f"⚠️ {module} - 可选模块未安装")
    
    return True

def test_gui():
    """测试GUI界面"""
    print("\n🔍 测试GUI界面...")
    
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("✅ GUI界面可以创建")
        return True
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

def create_test_video():
    """创建测试视频"""
    print("\n🔍 创建测试视频...")
    
    try:
        # 使用FFmpeg创建一个短测试视频
        cmd = [
            'ffmpeg', '-f', 'lavfi', '-i', 'testsrc=duration=5:size=320x240:rate=30',
            '-c:v', 'libx264', '-t', '5', '-y', 'test_video.mp4'
        ]
        
        # 如果系统FFmpeg不可用，尝试本地FFmpeg
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, timeout=2)
        except:
            cmd[0] = './ffmpeg.exe'
        
        result = subprocess.run(cmd, capture_output=True, timeout=30)
        
        if result.returncode == 0 and Path("test_video.mp4").exists():
            size_mb = os.path.getsize("test_video.mp4") / (1024 * 1024)
            print(f"✅ 测试视频创建成功 ({size_mb:.1f}MB)")
            return True
        else:
            print("❌ 测试视频创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建测试视频时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🎬 视频压缩工具 - 功能测试")
    print("="*50)
    
    tests = [
        ("Python环境", test_python),
        ("FFmpeg", test_ffmpeg), 
        ("Python依赖", test_dependencies),
        ("GUI界面", test_gui),
        ("创建测试视频", create_test_video)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n{'='*20} {name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"\n❌ {name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以正常使用视频压缩工具")
        
        # 询问是否运行主程序
        try:
            choice = input("\n是否运行主程序？(y/N): ").lower()
            if choice == 'y':
                import video_compressor
                video_compressor.main()
        except KeyboardInterrupt:
            pass
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
        print("\n建议操作:")
        print("1. 运行 python setup_and_build.py")
        print("2. 检查Python版本和依赖")
        print("3. 确保FFmpeg可用")
    
    # 清理测试文件
    try:
        if Path("test_video.mp4").exists():
            os.remove("test_video.mp4")
            print("\n🧹 清理测试文件")
    except:
        pass

if __name__ == "__main__":
    main() 