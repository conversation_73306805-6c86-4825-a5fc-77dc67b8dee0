#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频压缩工具 - 精确压缩到19.9MB
作者: AI助手
版本: 1.0
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import subprocess
import threading
import re
import json
import sys
from pathlib import Path

class VideoCompressor:
    def __init__(self, root):
        self.root = root
        self.root.title("视频压缩工具 - 精确压缩到19.9MB")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # 设置窗口图标和样式
        self.setup_ui()
        
        # 初始化变量
        self.input_file = ""
        self.output_file = ""
        self.video_duration = 0
        self.is_compressing = False
        
        # 检查FFmpeg
        self.check_ffmpeg()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="视频压缩工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="1. 选择视频文件", padding="10")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.file_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=50, state="readonly").grid(row=0, column=0, padx=(0, 10))
        ttk.Button(file_frame, text="选择文件", command=self.select_file).grid(row=0, column=1)
        
        # 拖拽提示
        drag_label = ttk.Label(file_frame, text="支持格式: MP4, AVI, MOV, MKV, WMV, FLV 等", 
                              font=("Arial", 9), foreground="gray")
        drag_label.grid(row=1, column=0, columnspan=2, pady=(5, 0))
        
        # 视频信息区域
        info_frame = ttk.LabelFrame(main_frame, text="2. 视频信息", padding="10")
        info_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.info_text = tk.Text(info_frame, height=6, width=60, state="disabled")
        scrollbar = ttk.Scrollbar(info_frame, orient="vertical", command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 压缩设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="3. 压缩设置", padding="10")
        settings_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 编码器选择
        ttk.Label(settings_frame, text="编码器:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.codec_var = tk.StringVar(value="AV1 (最高压缩比)")
        codec_combo = ttk.Combobox(settings_frame, textvariable=self.codec_var, 
                                  values=["AV1 (最高压缩比)", "H.265 (平衡)", "H.264 (兼容性最好)"], 
                                  state="readonly", width=20)
        codec_combo.grid(row=0, column=1, sticky=tk.W)
        
        # 质量预设
        ttk.Label(settings_frame, text="质量:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        self.quality_var = tk.StringVar(value="高质量")
        quality_combo = ttk.Combobox(settings_frame, textvariable=self.quality_var,
                                   values=["高质量", "平衡", "文件优先"], 
                                   state="readonly", width=20)
        quality_combo.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        # 输出设置
        ttk.Label(settings_frame, text="目标大小:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        ttk.Label(settings_frame, text="19.9 MB (固定)", font=("Arial", 9, "bold")).grid(row=2, column=1, sticky=tk.W, pady=(5, 0))
        
        # 压缩控制区域
        control_frame = ttk.LabelFrame(main_frame, text="4. 开始压缩", padding="10")
        control_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 状态标签
        self.status_var = tk.StringVar(value="请选择视频文件")
        self.status_label = ttk.Label(control_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        # 按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=2, column=0, columnspan=2)
        
        self.compress_button = ttk.Button(button_frame, text="开始压缩", 
                                        command=self.start_compression, state="disabled")
        self.compress_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.cancel_button = ttk.Button(button_frame, text="取消", 
                                      command=self.cancel_compression, state="disabled")
        self.cancel_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="打开输出文件夹", 
                  command=self.open_output_folder).pack(side=tk.LEFT)
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(0, weight=1)
        info_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(0, weight=1)
    
    def check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return True
        except:
            pass
        
        # 检查本地FFmpeg
        local_ffmpeg = Path("ffmpeg.exe")
        if local_ffmpeg.exists():
            return True
        
        self.show_ffmpeg_download_dialog()
        return False
    
    def show_ffmpeg_download_dialog(self):
        """显示FFmpeg下载对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("需要下载FFmpeg")
        dialog.geometry("400x200")
        dialog.resizable(False, False)
        dialog.grab_set()
        
        ttk.Label(dialog, text="首次使用需要下载FFmpeg", 
                 font=("Arial", 12, "bold")).pack(pady=10)
        
        ttk.Label(dialog, text="FFmpeg是开源的视频处理工具\n程序将自动下载并配置", 
                 justify=tk.CENTER).pack(pady=5)
        
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        
        ttk.Button(button_frame, text="自动下载", 
                  command=lambda: self.download_ffmpeg(dialog)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="手动下载", 
                  command=self.open_ffmpeg_website).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="跳过", 
                  command=dialog.destroy).pack(side=tk.LEFT, padx=5)
    
    def download_ffmpeg(self, dialog):
        """下载FFmpeg"""
        dialog.destroy()
        # 这里可以添加自动下载FFmpeg的代码
        messagebox.showinfo("提示", "请手动下载FFmpeg并放置到程序目录\n或运行安装脚本")
    
    def open_ffmpeg_website(self):
        """打开FFmpeg官网"""
        import webbrowser
        webbrowser.open("https://ffmpeg.org/download.html")
    
    def select_file(self):
        """选择视频文件"""
        filetypes = [
            ("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v"),
            ("所有文件", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=filetypes
        )
        
        if filename:
            self.input_file = filename
            self.file_path_var.set(filename)
            self.analyze_video()
    
    def analyze_video(self):
        """分析视频信息"""
        if not self.input_file:
            return
        
        self.update_status("正在分析视频...")
        
        # 在后台线程中分析视频
        threading.Thread(target=self._analyze_video_thread, daemon=True).start()
    
    def _analyze_video_thread(self):
        """分析视频的后台线程"""
        try:
            # 使用FFmpeg获取视频信息
            ffmpeg_cmd = 'ffmpeg'
            if not self.check_ffmpeg():
                ffmpeg_cmd = './ffmpeg.exe'
            
            cmd = [
                ffmpeg_cmd,
                '-i', self.input_file,
                '-f', 'null', '-'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            output = result.stderr  # FFmpeg的信息输出在stderr中
            
            # 解析视频信息
            info = self.parse_video_info(output)
            
            # 更新UI
            self.root.after(0, self.update_video_info, info)
            
        except Exception as e:
            self.root.after(0, self.update_status, f"视频分析失败: {str(e)}")
    
    def parse_video_info(self, ffmpeg_output):
        """解析FFmpeg输出的视频信息"""
        info = {}
        
        # 解析时长
        duration_match = re.search(r'Duration: (\d+):(\d+):(\d+\.\d+)', ffmpeg_output)
        if duration_match:
            hours, minutes, seconds = duration_match.groups()
            self.video_duration = int(hours) * 3600 + int(minutes) * 60 + float(seconds)
            info['duration'] = f"{hours}:{minutes}:{seconds}"
        
        # 解析分辨率
        resolution_match = re.search(r'(\d+)x(\d+)', ffmpeg_output)
        if resolution_match:
            width, height = resolution_match.groups()
            info['resolution'] = f"{width}x{height}"
        
        # 解析码率
        bitrate_match = re.search(r'bitrate: (\d+) kb/s', ffmpeg_output)
        if bitrate_match:
            info['bitrate'] = f"{bitrate_match.group(1)} kbps"
        
        # 解析编码格式
        codec_match = re.search(r'Video: (\w+)', ffmpeg_output)
        if codec_match:
            info['codec'] = codec_match.group(1)
        
        # 计算文件大小
        if os.path.exists(self.input_file):
            size_mb = os.path.getsize(self.input_file) / (1024 * 1024)
            info['size'] = f"{size_mb:.1f} MB"
        
        return info
    
    def update_video_info(self, info):
        """更新视频信息显示"""
        self.info_text.config(state="normal")
        self.info_text.delete(1.0, tk.END)
        
        text = "视频信息:\n"
        text += f"文件: {os.path.basename(self.input_file)}\n"
        
        if 'size' in info:
            text += f"当前大小: {info['size']}\n"
        if 'duration' in info:
            text += f"时长: {info['duration']}\n"
        if 'resolution' in info:
            text += f"分辨率: {info['resolution']}\n"
        if 'codec' in info:
            text += f"编码: {info['codec']}\n"
        if 'bitrate' in info:
            text += f"码率: {info['bitrate']}\n"
        
        # 计算目标码率
        if self.video_duration > 0:
            target_bitrate = int(19.9 * 8192 / self.video_duration)  # kbps
            audio_bitrate = 128  # 预留给音频
            video_bitrate = target_bitrate - audio_bitrate
            
            text += f"\n压缩设置:\n"
            text += f"目标大小: 19.9 MB\n"
            text += f"目标总码率: {target_bitrate} kbps\n"
            text += f"视频码率: {video_bitrate} kbps\n"
            text += f"音频码率: {audio_bitrate} kbps\n"
        
        self.info_text.insert(1.0, text)
        self.info_text.config(state="disabled")
        
        # 启用压缩按钮
        self.compress_button.config(state="normal")
        self.update_status("准备就绪，可以开始压缩")
    
    def start_compression(self):
        """开始压缩"""
        if not self.input_file or self.video_duration <= 0:
            messagebox.showerror("错误", "请先选择有效的视频文件")
            return
        
        # 选择输出文件
        output_file = filedialog.asksaveasfilename(
            title="保存压缩后的视频",
            defaultextension=".mp4",
            filetypes=[("MP4文件", "*.mp4"), ("MKV文件", "*.mkv")]
        )
        
        if not output_file:
            return
        
        self.output_file = output_file
        self.is_compressing = True
        
        # 更新UI状态
        self.compress_button.config(state="disabled")
        self.cancel_button.config(state="normal")
        self.progress_var.set(0)
        
        # 在后台线程中进行压缩
        threading.Thread(target=self._compress_video_thread, daemon=True).start()
    
    def _compress_video_thread(self):
        """压缩视频的后台线程"""
        try:
            # 计算码率
            target_bitrate = int(19.9 * 8192 / self.video_duration)  # kbps
            audio_bitrate = 128
            video_bitrate = target_bitrate - audio_bitrate
            
            # 选择编码器
            codec_map = {
                "AV1 (最高压缩比)": "libsvtav1",
                "H.265 (平衡)": "libx265", 
                "H.264 (兼容性最好)": "libx264"
            }
            video_codec = codec_map[self.codec_var.get()]
            
            # 第一遍分析
            self.root.after(0, self.update_status, "第一遍分析中...")
            
            # 获取FFmpeg命令
            ffmpeg_cmd = 'ffmpeg'
            try:
                subprocess.run(['ffmpeg', '-version'], capture_output=True, timeout=2)
            except:
                ffmpeg_cmd = './ffmpeg.exe'
            
            pass1_cmd = [
                ffmpeg_cmd,
                '-y', '-i', self.input_file,
                '-c:v', video_codec,
                '-b:v', f'{video_bitrate}k',
                '-pass', '1', '-an', '-f', 'null',
                '-' if os.name != 'nt' else 'NUL'
            ]
            
            subprocess.run(pass1_cmd, capture_output=True)
            
            if not self.is_compressing:
                return
            
            # 第二遍编码
            self.root.after(0, self.update_status, "第二遍编码中...")
            self.root.after(0, lambda: self.progress_var.set(50))
            
            pass2_cmd = [
                ffmpeg_cmd,
                '-y', '-i', self.input_file,
                '-c:v', video_codec,
                '-b:v', f'{video_bitrate}k',
                '-maxrate', f'{video_bitrate}k',
                '-bufsize', f'{video_bitrate * 2}k',
                '-pass', '2',
                '-c:a', 'aac', '-b:a', f'{audio_bitrate}k',
                '-movflags', '+faststart',
                self.output_file
            ]
            
            process = subprocess.Popen(pass2_cmd, stderr=subprocess.PIPE, text=True)
            
            # 监控进度
            while process.poll() is None and self.is_compressing:
                line = process.stderr.readline()
                if line:
                    # 解析进度信息
                    time_match = re.search(r'time=(\d+):(\d+):(\d+\.\d+)', line)
                    if time_match:
                        hours, minutes, seconds = time_match.groups()
                        current_time = int(hours) * 3600 + int(minutes) * 60 + float(seconds)
                        progress = min(50 + (current_time / self.video_duration) * 50, 100)
                        self.root.after(0, lambda p=progress: self.progress_var.set(p))
            
            if self.is_compressing:
                process.wait()
                
                if process.returncode == 0:
                    # 检查输出文件大小
                    if os.path.exists(self.output_file):
                        size_mb = os.path.getsize(self.output_file) / (1024 * 1024)
                        self.root.after(0, self.compression_completed, size_mb)
                    else:
                        self.root.after(0, self.compression_failed, "输出文件未生成")
                else:
                    self.root.after(0, self.compression_failed, "编码失败")
            
        except Exception as e:
            self.root.after(0, self.compression_failed, str(e))
        finally:
            # 清理临时文件
            try:
                os.remove("ffmpeg2pass-0.log")
                os.remove("ffmpeg2pass-0.log.mbtree")
            except:
                pass
    
    def compression_completed(self, size_mb):
        """压缩完成"""
        self.is_compressing = False
        self.progress_var.set(100)
        self.update_status(f"压缩完成！输出文件大小: {size_mb:.1f} MB")
        
        # 恢复按钮状态
        self.compress_button.config(state="normal")
        self.cancel_button.config(state="disabled")
        
        # 显示完成对话框
        result = messagebox.askyesno("压缩完成", 
                                   f"视频压缩完成！\n输出大小: {size_mb:.1f} MB\n\n是否打开输出文件夹？")
        if result:
            self.open_output_folder()
    
    def compression_failed(self, error):
        """压缩失败"""
        self.is_compressing = False
        self.progress_var.set(0)
        self.update_status(f"压缩失败: {error}")
        
        # 恢复按钮状态
        self.compress_button.config(state="normal")
        self.cancel_button.config(state="disabled")
        
        messagebox.showerror("压缩失败", f"视频压缩失败:\n{error}")
    
    def cancel_compression(self):
        """取消压缩"""
        self.is_compressing = False
        self.progress_var.set(0)
        self.update_status("已取消压缩")
        
        # 恢复按钮状态
        self.compress_button.config(state="normal")
        self.cancel_button.config(state="disabled")
    
    def open_output_folder(self):
        """打开输出文件夹"""
        if self.output_file and os.path.exists(self.output_file):
            folder = os.path.dirname(self.output_file)
            os.startfile(folder)
        else:
            os.startfile(os.path.expanduser("~/Desktop"))
    
    def update_info(self, message):
        """更新信息显示"""
        self.info_text.config(state="normal")
        self.info_text.insert(tk.END, message + "\n")
        self.info_text.config(state="disabled")
        self.info_text.see(tk.END)
    
    def update_status(self, status):
        """更新状态显示"""
        self.status_var.set(status)

def main():
    """主函数"""
    root = tk.Tk()
    app = VideoCompressor(root)
    
    # 设置窗口居中
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main() 