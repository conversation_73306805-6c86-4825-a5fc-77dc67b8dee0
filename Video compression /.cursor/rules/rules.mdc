---
description: 
globs: 
alwaysApply: false
---
---
description: 视频压缩工具项目规则
globs: 
alwaysApply: true
---

# 视频压缩工具项目规则

## 代码规范
- 使用Python 3.7+语法
- 遵循PEP 8编码规范
- 中文注释和文档字符串
- 错误处理要完整，用户友好

## 文件结构
- `video_compressor.py` - 主程序GUI界面
- `setup_and_build.py` - 自动安装打包脚本  
- `test_compressor.py` - 功能测试脚本
- `install.bat` - Windows一键安装脚本
- `requirements.txt` - Python依赖声明

## 技术要求
- GUI使用tkinter，保持Windows原生外观
- 视频处理必须使用FFmpeg
- 支持AV1/H.265/H.264三种编码器
- 必须使用2-pass编码确保文件大小精确
- 目标大小固定为19.9MB

## 用户体验
- 界面简洁，小白易用
- 提供详细的进度反馈
- 错误信息要友好易懂
- 支持拖拽和文件选择两种方式

## 打包要求
- 使用PyInstaller打包为单一exe文件
- 内嵌FFmpeg，避免用户手动下载
- 生成完整的发布包和说明文档
