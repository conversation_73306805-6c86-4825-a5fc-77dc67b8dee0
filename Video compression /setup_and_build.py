#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频压缩工具 - 自动安装和打包脚本
功能：
1. 自动下载FFmpeg
2. 安装Python依赖
3. 打包成Windows exe文件
"""

import os
import sys
import subprocess
import requests
import zipfile
import shutil
from pathlib import Path
import urllib.request

def print_step(step, message):
    """打印步骤信息"""
    print(f"\n{'='*50}")
    print(f"步骤 {step}: {message}")
    print(f"{'='*50}")

def check_python():
    """检查Python版本"""
    print_step(1, "检查Python环境")
    
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    return True

def install_dependencies():
    """安装Python依赖"""
    print_step(2, "安装Python依赖包")
    
    try:
        # 升级pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip", "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"], 
                      check=True)
        
        # 安装依赖
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt", "-i", "https://pypi.tuna.tsinghua.edu.cn/simple"], 
                      check=True)
        
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def download_ffmpeg():
    """下载FFmpeg"""
    print_step(3, "下载FFmpeg")
    
    if Path("ffmpeg.exe").exists():
        print("✅ FFmpeg已存在，跳过下载")
        return True
    
    try:
        print("正在下载FFmpeg...")
        
        # FFmpeg Windows静态构建下载地址
        ffmpeg_url = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
        
        # 下载文件
        print("正在下载FFmpeg压缩包...")
        response = requests.get(ffmpeg_url, stream=True)
        response.raise_for_status()
        
        with open("ffmpeg.zip", "wb") as f:
            total_length = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_length > 0:
                        progress = (downloaded / total_length) * 100
                        print(f"\r下载进度: {progress:.1f}%", end="", flush=True)
        
        print("\n正在解压FFmpeg...")
        
        # 解压文件
        with zipfile.ZipFile("ffmpeg.zip", 'r') as zip_ref:
            zip_ref.extractall("temp_ffmpeg")
        
        # 查找ffmpeg.exe
        ffmpeg_path = None
        for root, dirs, files in os.walk("temp_ffmpeg"):
            if "ffmpeg.exe" in files:
                ffmpeg_path = os.path.join(root, "ffmpeg.exe")
                break
        
        if ffmpeg_path:
            shutil.copy2(ffmpeg_path, "ffmpeg.exe")
            print("✅ FFmpeg下载并配置成功")
        else:
            print("❌ 未在压缩包中找到ffmpeg.exe")
            return False
        
        # 清理临时文件
        os.remove("ffmpeg.zip")
        shutil.rmtree("temp_ffmpeg")
        
        return True
        
    except Exception as e:
        print(f"❌ FFmpeg下载失败: {e}")
        print("\n可以手动下载FFmpeg:")
        print("1. 访问: https://ffmpeg.org/download.html")
        print("2. 下载Windows版本")
        print("3. 将ffmpeg.exe复制到此目录")
        return False

def build_executable():
    """打包成可执行文件"""
    print_step(4, "打包成Windows可执行文件")
    
    try:
        # 创建打包命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",  # 打包成单个文件
            "--windowed",  # 窗口模式，不显示控制台
            "--name", "视频压缩工具",
            "--icon", "icon.ico" if Path("icon.ico").exists() else None,
            "--add-data", "ffmpeg.exe;.",  # 包含FFmpeg
            "video_compressor.py"
        ]
        
        # 移除None值
        cmd = [arg for arg in cmd if arg is not None]
        
        print("正在打包...")
        subprocess.run(cmd, check=True)
        
        print("✅ 打包成功！")
        print("📁 可执行文件位置: dist/视频压缩工具.exe")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        return False

def create_installer():
    """创建安装包"""
    print_step(5, "创建安装包")
    
    try:
        # 创建发布目录
        release_dir = Path("release")
        release_dir.mkdir(exist_ok=True)
        
        # 复制文件
        exe_file = Path("dist/视频压缩工具.exe")
        if exe_file.exists():
            shutil.copy2(exe_file, release_dir / "视频压缩工具.exe")
        
        # 创建使用说明
        readme_content = """# 视频压缩工具使用说明

## 功能介绍
本工具可以将任何视频文件精确压缩到19.9MB，适合微信等平台的文件大小限制。

## 使用步骤
1. 运行"视频压缩工具.exe"
2. 点击"选择文件"按钮，选择要压缩的视频
3. 查看视频信息和压缩预览
4. 选择合适的编码器和质量设置
5. 点击"开始压缩"并选择保存位置
6. 等待压缩完成

## 编码器说明
- **AV1**: 最高压缩比，文件最小，但编码时间较长
- **H.265**: 压缩比和速度的平衡选择
- **H.264**: 兼容性最好，编码速度最快

## 质量设置
- **高质量**: 保持更好的画面质量，适合重要视频
- **平衡**: 质量和大小的平衡
- **文件优先**: 优先保证文件大小，质量稍有降低

## 技术原理
工具使用FFmpeg进行视频编码，采用2-pass编码技术确保文件大小精确控制在19.9MB。

## 注意事项
1. 首次使用会自动下载FFmpeg（约100MB）
2. 压缩时间取决于视频长度和选择的编码器
3. 原视频时长越长，压缩后的码率越低
4. 建议原视频时长不超过10分钟，以保证较好的画质

## 系统要求
- Windows 7/8/10/11
- 至少2GB可用内存
- 至少1GB磁盘空间（用于临时文件）

## 技术支持
如有问题，请检查：
1. 是否有足够的磁盘空间
2. 视频文件是否损坏
3. 杀毒软件是否误杀FFmpeg
"""
        
        with open(release_dir / "使用说明.txt", "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print("✅ 安装包创建成功！")
        print(f"📁 安装包位置: {release_dir.absolute()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 安装包创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🎬 视频压缩工具 - 自动构建脚本")
    print("功能：将任何视频精确压缩到19.9MB")
    
    # 检查运行环境
    if not check_python():
        input("\n按Enter键退出...")
        return
    
    # 安装依赖
    if not install_dependencies():
        input("\n按Enter键退出...")
        return
    
    # 下载FFmpeg
    if not download_ffmpeg():
        choice = input("\n是否继续打包？(y/N): ").lower()
        if choice != 'y':
            return
    
    # 打包程序
    if not build_executable():
        input("\n按Enter键退出...")
        return
    
    # 创建安装包
    create_installer()
    
    print(f"\n{'='*50}")
    print("🎉 构建完成！")
    print("📁 可执行文件: dist/视频压缩工具.exe")
    print("📦 安装包: release/")
    print(f"{'='*50}")
    
    input("\n按Enter键退出...")

if __name__ == "__main__":
    main() 