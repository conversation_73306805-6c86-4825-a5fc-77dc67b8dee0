@echo off
chcp 65001 >nul
title 视频压缩工具 - 一键安装脚本

echo.
echo ================================
echo   视频压缩工具 - 一键安装
echo ================================
echo.
echo 本工具可以将任何视频精确压缩到19.9MB
echo 适合微信等平台的文件大小限制
echo.

:: 检查Python
echo [1/4] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.7或更高版本
    echo.
    echo 下载地址: https://www.python.org/downloads/
    echo 安装时请勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

:: 安装依赖和打包
echo.
echo [2/4] 安装Python依赖包...
python setup_and_build.py

:: 检查是否成功
if exist "dist\视频压缩工具.exe" (
    echo.
    echo ================================
    echo      🎉 安装成功！
    echo ================================
    echo.
    echo 📁 程序位置: dist\视频压缩工具.exe
    echo 📦 完整安装包: release\
    echo.
    echo 现在可以运行视频压缩工具了！
    echo.
    
    choice /C YN /M "是否立即运行程序？(Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 (
        start "" "dist\视频压缩工具.exe"
    )
) else (
    echo.
    echo ❌ 安装失败，请检查错误信息
    echo.
)

:end
echo.
pause 