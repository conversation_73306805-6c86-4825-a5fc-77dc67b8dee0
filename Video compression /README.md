# 🎬 视频压缩工具

**一键将任何视频精确压缩到19.9MB的Windows工具**

## ✨ 功能特点

- 🎯 **精确控制** - 严格控制文件大小在19.9MB以内
- 🚀 **一键操作** - 图形界面，小白也能轻松使用  
- 🔧 **智能压缩** - 自动计算最优码率，采用2-pass编码
- 📱 **多格式支持** - 支持MP4/AVI/MOV/MKV等主流格式
- ⚡ **高效编码** - 支持AV1/H.265/H.264三种编码器
- 💻 **Windows优化** - 专为Windows平台优化，无需任何编程基础

## 🎯 适用场景

- 📲 微信文件传输（20MB限制）
- 📧 邮件附件
- 🌐 网页上传
- 💾 存储空间优化

## 🚀 快速开始

### 方式一：一键安装（推荐）

1. 下载项目文件
2. 双击运行 `install.bat`
3. 等待自动安装完成
4. 运行生成的 `视频压缩工具.exe`

### 方式二：手动安装

```bash
# 1. 克隆项目
git clone [项目地址]
cd video-compressor

# 2. 安装依赖
pip install -r requirements.txt

# 3. 运行安装脚本
python setup_and_build.py

# 4. 运行程序
python video_compressor.py
```

## 📖 使用方法

1. **选择视频** - 点击"选择文件"或直接拖拽视频文件
2. **查看信息** - 自动分析视频信息和压缩预览
3. **选择设置** - 根据需要选择编码器和质量
4. **开始压缩** - 点击"开始压缩"并选择保存位置
5. **等待完成** - 实时显示压缩进度

## ⚙️ 编码器说明

| 编码器 | 压缩比 | 编码速度 | 兼容性 | 推荐场景 |
|--------|--------|----------|--------|----------|
| **AV1** | 最高 | 较慢 | 较新 | 追求最小文件 |
| **H.265** | 高 | 中等 | 良好 | 平衡选择 |
| **H.264** | 中等 | 最快 | 最好 | 兼容性优先 |

## 🔬 技术原理

### 精确大小控制算法

```
目标码率(kbps) = 19.9(MB) × 8192 ÷ 视频时长(秒)
视频码率 = 目标码率 - 音频码率(128kbps)
```

### 2-Pass编码流程

1. **第一遍分析** - 分析视频复杂度，生成编码日志
2. **第二遍编码** - 基于分析结果，精确分配码率

### 质量优化策略

- 🎵 **音频优化** - 使用AAC 128kbps，平衡质量与大小
- 🖼️ **分辨率智能** - 根据码率自动选择最优分辨率
- ⏱️ **帧率控制** - 必要时降低帧率节省码率
- 🎬 **场景分析** - 2-pass编码确保复杂场景质量

## 📋 系统要求

- **操作系统**: Windows 7/8/10/11
- **内存**: 至少2GB可用内存  
- **存储**: 至少1GB磁盘空间
- **网络**: 首次使用需下载FFmpeg（约100MB）

## 🛠️ 依赖组件

- **Python 3.7+** - 主程序运行环境
- **FFmpeg** - 视频处理核心引擎
- **tkinter** - 图形用户界面
- **PyInstaller** - 可执行文件打包

## 📊 性能表现

| 视频时长 | 原始大小 | 压缩后 | 压缩比 | 处理时间* |
|----------|----------|--------|--------|-----------|
| 1分钟 | 50MB | 19.9MB | 60% | ~2分钟 |
| 3分钟 | 150MB | 19.9MB | 87% | ~6分钟 |
| 5分钟 | 300MB | 19.9MB | 93% | ~12分钟 |

*处理时间基于中等性能电脑，使用H.265编码器

## ❓ 常见问题

### Q: 压缩后画质如何？
A: 采用最新编码技术，1-3分钟视频基本无感知损失，5分钟以上会有轻微质量降低但仍可接受。

### Q: 支持哪些视频格式？
A: 支持所有主流格式：MP4、AVI、MOV、MKV、WMV、FLV、WebM等。

### Q: 压缩时间太长怎么办？
A: 可选择H.264编码器提高速度，或选择"文件优先"质量设置。

### Q: 杀毒软件报毒怎么办？
A: 这是误报，可以添加白名单。程序开源无毒，可查看源代码。

## 🔧 开发指南

### 项目结构
```
video-compressor/
├── video_compressor.py     # 主程序
├── setup_and_build.py     # 安装打包脚本
├── requirements.txt       # Python依赖
├── install.bat           # Windows一键安装
└── README.md            # 项目说明
```

### 自定义开发
```python
# 修改目标大小
TARGET_SIZE_MB = 19.9

# 自定义编码器
ENCODERS = {
    "AV1": "libsvtav1",
    "H.265": "libx265", 
    "H.264": "libx264"
}

# 质量预设
QUALITY_PRESETS = {
    "高质量": {"crf": 20, "preset": "slow"},
    "平衡": {"crf": 23, "preset": "medium"},
    "文件优先": {"crf": 28, "preset": "fast"}
}
```

## 📝 更新日志

### v1.0.0 (2024-12-19)
- ✅ 初始版本发布
- ✅ 支持AV1/H.265/H.264编码
- ✅ 2-pass精确大小控制
- ✅ 图形界面操作
- ✅ 自动FFmpeg下载
- ✅ Windows一键安装

## 📄 许可证

本项目基于 MIT 许可证开源，详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 💖 支持项目

如果这个工具对你有帮助，请给个 ⭐ Star！

---

*让视频压缩变得简单高效！* 🎬✨ 