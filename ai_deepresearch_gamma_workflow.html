<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepResearch × Gamma：AI驱动的专业PPT制作流程</title>
    
    <!-- 外部资源 -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/mermaid@latest/dist/mermaid.min.js"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
        
        body {
            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
            line-height: 1.7;
            scroll-behavior: smooth;
        }
        
        .serif-font { font-family: 'Noto Serif SC', serif; }
        
        .gradient-text {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero-section {
            background: var(--primary-gradient);
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><rect width="100" height="100" fill="%23ffffff" opacity="0.03"/><circle cx="20" cy="20" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="80" cy="50" r="1.5" fill="%23ffffff" opacity="0.08"/><circle cx="45" cy="80" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
        }
        
        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card-hover {
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        
        .card-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
        
        .step-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-left: 4px solid transparent;
            border-image: var(--primary-gradient) 1;
        }
        
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            grid-auto-rows: minmax(180px, auto);
        }
        
        .bento-card {
            border-radius: 16px;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .bento-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
            background: var(--primary-gradient);
        }
        
        .bento-large { grid-column: span 2; }
        .bento-tall { grid-row: span 2; }
        
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .slide-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease-out;
        }
        
        .slide-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .mermaid {
            background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        @media (max-width: 1024px) {
            .bento-grid {
                grid-template-columns: repeat(3, 1fr);
            }
            .bento-large { grid-column: span 2; }
        }
        
        @media (max-width: 768px) {
            .bento-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            .bento-large { grid-column: span 2; }
            .bento-tall { grid-row: span 1; }
        }
        
        @media (max-width: 480px) {
            .bento-grid {
                grid-template-columns: 1fr;
            }
            .bento-large { grid-column: span 1; }
            .bento-tall { grid-row: span 1; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Hero Section -->
    <section class="hero-section min-h-screen flex items-center justify-center relative">
        <div class="container mx-auto px-6 text-center relative z-10">
            <div class="floating-animation">
                <i class="fas fa-robot text-6xl text-white mb-8 opacity-90"></i>
            </div>
            <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 serif-font">
                DeepResearch × Gamma
            </h1>
            <h2 class="text-2xl md:text-3xl text-gray-100 mb-8 font-light">
                AI驱动的专业PPT制作革命
            </h2>
            <p class="text-lg md:text-xl text-gray-200 max-w-3xl mx-auto mb-12 leading-relaxed">
                30分钟内完成从行业调研到PPT输出的全过程，效率提升10倍以上的AI工作流程
            </p>
            <div class="glass-effect rounded-2xl p-8 max-w-4xl mx-auto">
                <div class="grid md:grid-cols-3 gap-6 text-center">
                    <div>
                        <i class="fas fa-search text-3xl text-white mb-3"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">智能调研</h3>
                        <p class="text-gray-200 text-sm">Deep Research自动化信息收集</p>
                    </div>
                    <div>
                        <i class="fas fa-magic text-3xl text-white mb-3"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">自动生成</h3>
                        <p class="text-gray-200 text-sm">Gamma智能PPT制作</p>
                    </div>
                    <div>
                        <i class="fas fa-chart-line text-3xl text-white mb-3"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">效率提升</h3>
                        <p class="text-gray-200 text-sm">10倍效率提升保证</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 第一步：选择研究方案 -->
    <section class="py-20 bg-gradient-to-br from-blue-50 to-purple-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 slide-in">
                <h2 class="text-4xl font-bold mb-4 gradient-text serif-font">第一步：选择最适合的研究方案</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    根据你的研究需求，选择Gemini或ChatGPT的Deep Research功能
                </p>
            </div>
            
            <div class="grid lg:grid-cols-2 gap-8 mb-16">
                <!-- Gemini Deep Research -->
                <div class="step-card rounded-2xl p-8 card-hover slide-in">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-4">
                            <i class="fab fa-google text-white text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800">Gemini Deep Research</h3>
                    </div>
                    <h4 class="text-lg font-semibold text-blue-600 mb-4">广度优先的全景分析</h4>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-globe text-blue-500 mt-1 mr-3"></i>
                            <div>
                                <p class="font-medium text-gray-800">覆盖面广</p>
                                <p class="text-gray-600 text-sm">从市场背景、玩家特征、技术趋势等多维度分析</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-search text-blue-500 mt-1 mr-3"></i>
                            <div>
                                <p class="font-medium text-gray-800">自动验证</p>
                                <p class="text-gray-600 text-sm">浏览数百个网站，交叉验证信息准确性</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-chart-pie text-blue-500 mt-1 mr-3"></i>
                            <div>
                                <p class="font-medium text-gray-800">全景概览</p>
                                <p class="text-gray-600 text-sm">适合初次接触行业，建立完整知识框架</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- ChatGPT Deep Research -->
                <div class="step-card rounded-2xl p-8 card-hover slide-in">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-brain text-white text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800">ChatGPT Deep Research</h3>
                    </div>
                    <h4 class="text-lg font-semibold text-green-600 mb-4">精度优先的深度挖掘</h4>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <i class="fas fa-crosshairs text-green-500 mt-1 mr-3"></i>
                            <div>
                                <p class="font-medium text-gray-800">精准定位</p>
                                <p class="text-gray-600 text-sm">交互式问题细化，确保研究方向精准匹配</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-microscope text-green-500 mt-1 mr-3"></i>
                            <div>
                                <p class="font-medium text-gray-800">深度挖掘</p>
                                <p class="text-gray-600 text-sm">识别新产品具体信息，包括开发商、发行商等</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-bullseye text-green-500 mt-1 mr-3"></i>
                            <div>
                                <p class="font-medium text-gray-800">目标明确</p>
                                <p class="text-gray-600 text-sm">适合有明确研究目标的深度分析项目</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 选择策略 -->
            <div class="bg-white rounded-2xl p-8 shadow-xl slide-in">
                <h3 class="text-2xl font-bold text-center mb-8 gradient-text">选择策略指南</h3>
                <div class="grid md:grid-cols-2 gap-8">
                    <div class="text-center p-6 bg-blue-50 rounded-xl">
                        <i class="fas fa-eye text-3xl text-blue-500 mb-4"></i>
                        <h4 class="text-lg font-semibold text-blue-600 mb-2">想要全面了解选Gemini</h4>
                        <p class="text-gray-600">适合初次接触某个行业，需要建立完整知识框架</p>
                    </div>
                    <div class="text-center p-6 bg-green-50 rounded-xl">
                        <i class="fas fa-telescope text-3xl text-green-500 mb-4"></i>
                        <h4 class="text-lg font-semibold text-green-600 mb-2">想要深度分析选GPT</h4>
                        <p class="text-gray-600">适合有明确研究目标，需要精准数据支撑决策</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 第二步：Gamma制作PPT -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 slide-in">
                <h2 class="text-4xl font-bold mb-4 gradient-text serif-font">第二步：Gamma制作PPT的完整流程</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    2025年最领先的AI演示文稿制作工具，智能化程度远超竞争对手
                </p>
            </div>
            
            <div class="space-y-12">
                <!-- Gamma平台介绍 -->
                <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-8 slide-in">
                    <h3 class="text-2xl font-bold mb-6 text-purple-600">Gamma平台核心优势</h3>
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="text-center">
                            <i class="fas fa-cogs text-3xl text-purple-500 mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">先进AI技术</h4>
                            <p class="text-gray-600 text-sm">基于最新AI内容生成技术</p>
                        </div>
                        <div class="text-center">
                            <i class="fas fa-file-export text-3xl text-purple-500 mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">多格式输出</h4>
                            <p class="text-gray-600 text-sm">支持PPT、网页、文档等格式</p>
                        </div>
                        <div class="text-center">
                            <i class="fas fa-image text-3xl text-purple-500 mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">智能图像生成</h4>
                            <p class="text-gray-600 text-sm">内置高质量图像生成功能</p>
                        </div>
                        <div class="text-center">
                            <i class="fas fa-palette text-3xl text-purple-500 mb-3"></i>
                            <h4 class="font-semibold text-gray-800 mb-2">智能设计</h4>
                            <p class="text-gray-600 text-sm">自动版式设计和品牌一致性</p>
                        </div>
                    </div>
                </div>
                
                <!-- 操作步骤 -->
                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- 步骤1-2 -->
                    <div class="space-y-6">
                        <div class="step-card rounded-xl p-6 card-hover slide-in">
                            <div class="flex items-center mb-4">
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center font-bold mr-3">1</div>
                                <h4 class="text-lg font-semibold">导入内容</h4>
                            </div>
                            <p class="text-gray-600">打开Gamma，选择"粘贴文本"，将完整研究报告复制进来，系统自动分析内容结构</p>
                        </div>
                        
                        <div class="step-card rounded-xl p-6 card-hover slide-in">
                            <div class="flex items-center mb-4">
                                <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center font-bold mr-3">2</div>
                                <h4 class="text-lg font-semibold">内容处理技巧</h4>
                            </div>
                            <div class="space-y-2 text-gray-600">
                                <p>• 先选择"保留原文"模式，让Gamma自动切割分页</p>
                                <p>• 基于内容逻辑自动划分章节</p>
                                <p>• 完成后切换回"压缩模式"</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 步骤3-4 -->
                    <div class="space-y-6">
                        <div class="step-card rounded-xl p-6 card-hover slide-in">
                            <div class="flex items-center mb-4">
                                <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center font-bold mr-3">3</div>
                                <h4 class="text-lg font-semibold">图像生成设置</h4>
                            </div>
                            <p class="text-gray-600">建议选择GPT Image模型，指令遵循能力最强，生成图片高度贴合PPT内容</p>
                        </div>
                        
                        <div class="step-card rounded-xl p-6 card-hover slide-in">
                            <div class="flex items-center mb-4">
                                <div class="w-8 h-8 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full flex items-center justify-center font-bold mr-3">4</div>
                                <h4 class="text-lg font-semibold">模板和风格选择</h4>
                            </div>
                            <p class="text-gray-600">从丰富的专业模板中选择，点击生成开始自动化创建过程</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 工作流程可视化 -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center mb-4 gradient-text serif-font">完整AI工作流程概览</h2>
            <p class="text-gray-600 text-center mb-12 max-w-2xl mx-auto">
                从需求分析到最终交付的完整AI驱动流程
            </p>
            <div class="mermaid text-center">
                graph LR
                    A[确定研究主题] --> B{选择AI工具}
                    B -->|广度分析| C[Gemini Deep Research]
                    B -->|精度分析| D[ChatGPT Deep Research]
                    C --> E[生成研究报告]
                    D --> E
                    E --> F[导入Gamma平台]
                    F --> G[AI自动生成PPT]
                    G --> H[专业PPT输出]
                    
                    style A fill:#667eea,stroke:#333,stroke-width:2px,color:#fff
                    style H fill:#764ba2,stroke:#333,stroke-width:2px,color:#fff
                    style B fill:#f093fb,stroke:#333,stroke-width:2px,color:#fff
                    style E fill:#4facfe,stroke:#333,stroke-width:2px,color:#fff
                    style F fill:#f093fb,stroke:#333,stroke-width:2px,color:#fff
                    style G fill:#764ba2,stroke:#333,stroke-width:2px,color:#fff
            </div>
        </div>
    </section>

    <!-- 为什么这套流程高效 -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 slide-in">
                <h2 class="text-4xl font-bold mb-4 gradient-text serif-font">为什么这套AI流程如此高效？</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    从信息获取到内容产出的全流程优化
                </p>
            </div>
            
            <div class="grid lg:grid-cols-3 gap-8 mb-12">
                <div class="bg-white rounded-2xl p-8 shadow-lg card-hover slide-in">
                    <i class="fas fa-rocket text-4xl text-blue-500 mb-6"></i>
                    <h3 class="text-xl font-bold mb-4">全流程提效</h3>
                    <ul class="space-y-3 text-gray-600">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>信息获取高效：自动化替代手动搜索</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>内容创作高效：消除设计排版耗时</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>迭代优化高效：支持快速修改试验</span>
                        </li>
                    </ul>
                </div>
                
                <div class="bg-white rounded-2xl p-8 shadow-lg card-hover slide-in">
                    <i class="fas fa-shield-alt text-4xl text-green-500 mb-6"></i>
                    <h3 class="text-xl font-bold mb-4">高质量保障</h3>
                    <ul class="space-y-3 text-gray-600">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>多源验证：跨平台交叉验证信息</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>专业设计：内置模板遵循设计规范</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>风格一致：AI统一掌控文档逻辑</span>
                        </li>
                    </ul>
                </div>
                
                <div class="bg-white rounded-2xl p-8 shadow-lg card-hover slide-in">
                    <i class="fas fa-globe text-4xl text-purple-500 mb-6"></i>
                    <h3 class="text-xl font-bold mb-4">适用范围广</h3>
                    <ul class="space-y-3 text-gray-600">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>消费品市场研究</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>技术趋势洞察</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mt-1 mr-2"></i>
                            <span>投资机会评估</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 质量提升表格 -->
            <div class="bg-white rounded-2xl p-8 shadow-lg slide-in">
                <h3 class="text-2xl font-bold text-center mb-8 gradient-text">质量提升四大维度</h3>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b-2 border-gray-200">
                                <th class="text-left py-4 px-6 font-semibold text-gray-800">维度</th>
                                <th class="text-left py-4 px-6 font-semibold text-gray-800">AI带来的提升</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-100">
                            <tr class="hover:bg-gray-50">
                                <td class="py-4 px-6 font-medium text-blue-600">信息全面性</td>
                                <td class="py-4 px-6 text-gray-600">覆盖更多维度、更多渠道</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="py-4 px-6 font-medium text-green-600">数据准确性</td>
                                <td class="py-4 px-6 text-gray-600">多源验证，降低错误率</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="py-4 px-6 font-medium text-purple-600">设计专业性</td>
                                <td class="py-4 px-6 text-gray-600">统一模板，视觉专业性提升</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="py-4 px-6 font-medium text-red-600">逻辑一致性</td>
                                <td class="py-4 px-6 text-gray-600">风格统一、逻辑清晰</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>

    <!-- Bento Grid 总结区域 -->
    <section class="py-20 bg-gradient-to-br from-purple-100 via-blue-50 to-indigo-100">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 slide-in">
                <h2 class="text-4xl font-bold mb-4 gradient-text serif-font">总结与展望</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    掌握AI时代的正确工具用法，成为你的竞争优势
                </p>
            </div>
            
            <div class="bento-grid">
                <!-- 核心价值 -->
                <div class="bento-card bento-large bg-gradient-to-br from-blue-400 to-purple-500 text-white slide-in">
                    <i class="fas fa-crown text-4xl mb-6 opacity-90"></i>
                    <h3 class="text-2xl font-bold mb-4">核心价值</h3>
                    <p class="text-lg leading-relaxed">
                        革命性AI工作流程，<span class="font-bold">30分钟内</span>从行业调研到专业PPT制作一站式完成，
                        让知识工作者快速掌握任何领域，构建深度认知体系
                    </p>
                </div>
                
                <!-- 时间效率 -->
                <div class="bento-card bg-white shadow-lg slide-in">
                    <i class="fas fa-clock text-3xl text-green-500 mb-4"></i>
                    <h3 class="text-xl font-bold mb-3 text-gray-800">时间效率</h3>
                    <div class="text-3xl font-bold text-green-500 mb-2">30分钟</div>
                    <p class="text-gray-600">从调研到PPT全流程</p>
                </div>
                
                <!-- 效率提升 -->
                <div class="bento-card bg-white shadow-lg slide-in">
                    <i class="fas fa-chart-line text-3xl text-blue-500 mb-4"></i>
                    <h3 class="text-xl font-bold mb-3 text-gray-800">效率提升</h3>
                    <div class="text-3xl font-bold text-blue-500 mb-2">10倍+</div>
                    <p class="text-gray-600">相比传统调研+制作</p>
                </div>
                
                <!-- 方法论意义 -->
                <div class="bento-card bento-tall bg-gradient-to-br from-green-400 to-blue-500 text-white slide-in">
                    <i class="fas fa-lightbulb text-4xl mb-6 opacity-90"></i>
                    <h3 class="text-2xl font-bold mb-4">方法论意义</h3>
                    <p class="leading-relaxed">
                        Deep Research + Gamma的组合代表了信息时代工作方式的根本变革：
                        从手工搜索到AI调研，从手动设计到智能生成，重新定义专业内容生产标准
                    </p>
                </div>
                
                <!-- 适用场景 -->
                <div class="bento-card bg-white shadow-lg slide-in">
                    <i class="fas fa-users text-3xl text-purple-500 mb-4"></i>
                    <h3 class="text-xl font-bold mb-3 text-gray-800">适用场景</h3>
                    <ul class="text-gray-600 space-y-1">
                        <li>• 4X SLG游戏趋势分析</li>
                        <li>• 消费品市场调研</li>
                        <li>• 投资机会评估</li>
                        <li>• 技术趋势洞察</li>
                    </ul>
                </div>
                
                <!-- 技术优势 -->
                <div class="bento-card bg-gradient-to-br from-purple-400 to-indigo-500 text-white slide-in">
                    <i class="fas fa-cogs text-3xl mb-4 opacity-90"></i>
                    <h3 class="text-xl font-bold mb-3">技术优势</h3>
                    <p>Gemini 2.5 Pro深度调研引擎，GPT精准分析能力，Gamma智能PPT生成，三大AI技术栈完美融合</p>
                </div>
                
                <!-- 未来展望 -->
                <div class="bento-card bg-gradient-to-br from-yellow-400 to-orange-500 text-white slide-in">
                    <i class="fas fa-telescope text-3xl mb-4 opacity-90"></i>
                    <h3 class="text-xl font-bold mb-3">持续优化</h3>
                    <p>随着Gemini、GPT、Gamma持续升级，工作流程将更加智能化，从研究质量到生成效果全面提升</p>
                </div>
                
                <!-- 关键洞察 -->
                <div class="bento-card bento-large bg-gradient-to-br from-red-400 to-pink-500 text-white slide-in">
                    <i class="fas fa-quote-left text-3xl mb-4 opacity-75"></i>
                    <blockquote class="text-xl italic leading-relaxed">
                        "DeepResearch × Gamma不仅是一套工具组合，更是<strong>AI时代知识工作的新范式</strong>。
                        它让每个人都能在30分钟内成为任何领域的专家级分析师。"
                    </blockquote>
                </div>
                
                <!-- 投资回报 -->
                <div class="bento-card bg-gradient-to-br from-teal-400 to-cyan-500 text-white slide-in">
                    <i class="fas fa-trophy text-3xl mb-4 opacity-90"></i>
                    <h3 class="text-xl font-bold mb-3">投资回报</h3>
                    <div class="text-2xl font-bold mb-2">ROI 1000%+</div>
                    <p class="text-sm opacity-90">30分钟=传统方法5小时</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-6 text-center">
            <div class="mb-8">
                <i class="fas fa-robot text-3xl text-blue-400 mb-4"></i>
                <h3 class="text-xl font-bold mb-2">AI驱动的未来工作方式</h3>
                <p class="text-gray-400">让技术为创造力服务</p>
            </div>
            <div class="border-t border-gray-700 pt-8">
                <p class="text-gray-400">
                    © 2025 DeepResearch × Gamma 工作流程指南
                </p>
            </div>
        </div>
    </footer>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // 观察所有需要动画的元素
        document.querySelectorAll('.slide-in').forEach(el => {
            observer.observe(el);
        });

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 添加动态效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为卡片添加鼠标跟踪效果
            document.querySelectorAll('.card-hover').forEach(card => {
                card.addEventListener('mousemove', function(e) {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    
                    const rotateX = (y - centerY) / 10;
                    const rotateY = (centerX - x) / 10;
                    
                    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-5px)`;
                });
                
                card.addEventListener('mouseleave', function() {
                    card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
                });
            });
        });
    </script>
</body>
</html> 