<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome & Tailwind CSS 测试</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdn.bytedance.com/font-awesome/6.7.2/css/all.min.css">
    
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="https://cdn.bytedance.com/tailwindcss/3.4.17/tailwind.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <h1 class="text-4xl font-bold text-center text-blue-600 mb-8">
            <i class="fas fa-rocket mr-3"></i>
            Font Awesome & Tailwind CSS 测试
        </h1>
        
        <!-- 卡片容器 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 卡片1 - Font Awesome 图标测试 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">
                    <i class="fas fa-icons text-purple-500 mr-2"></i>
                    Font Awesome 图标
                </h2>
                <div class="grid grid-cols-4 gap-4 text-2xl text-center">
                    <i class="fas fa-home text-blue-500"></i>
                    <i class="fas fa-user text-green-500"></i>
                    <i class="fas fa-heart text-red-500"></i>
                    <i class="fas fa-star text-yellow-500"></i>
                    <i class="fab fa-github text-gray-800"></i>
                    <i class="fab fa-twitter text-blue-400"></i>
                    <i class="fas fa-envelope text-indigo-500"></i>
                    <i class="fas fa-phone text-green-600"></i>
                </div>
            </div>
            
            <!-- 卡片2 - Tailwind CSS 样式测试 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">
                    <i class="fas fa-palette text-pink-500 mr-2"></i>
                    Tailwind CSS 样式
                </h2>
                <div class="space-y-3">
                    <button class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-300">
                        <i class="fas fa-click mr-2"></i>
                        蓝色按钮
                    </button>
                    <button class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-300">
                        <i class="fas fa-check mr-2"></i>
                        绿色按钮
                    </button>
                    <button class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-300">
                        <i class="fas fa-times mr-2"></i>
                        红色按钮
                    </button>
                </div>
            </div>
            
            <!-- 卡片3 - 组合效果 -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">
                    <i class="fas fa-magic text-purple-500 mr-2"></i>
                    组合效果
                </h2>
                <div class="space-y-4">
                    <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                        <i class="fas fa-info-circle text-blue-500 mr-3"></i>
                        <span class="text-blue-700">信息提示</span>
                    </div>
                    <div class="flex items-center p-3 bg-green-50 rounded-lg">
                        <i class="fas fa-check-circle text-green-500 mr-3"></i>
                        <span class="text-green-700">成功消息</span>
                    </div>
                    <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-yellow-500 mr-3"></i>
                        <span class="text-yellow-700">警告信息</span>
                    </div>
                    <div class="flex items-center p-3 bg-red-50 rounded-lg">
                        <i class="fas fa-times-circle text-red-500 mr-3"></i>
                        <span class="text-red-700">错误信息</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部信息 -->
        <div class="mt-12 text-center">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">
                    <i class="fas fa-code text-indigo-500 mr-2"></i>
                    CDN 链接测试状态
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="p-4 bg-gray-50 rounded-lg">
                        <p class="font-medium text-gray-700">
                            <i class="fab fa-font-awesome text-blue-600 mr-2"></i>
                            Font Awesome 6.7.2
                        </p>
                        <p class="text-sm text-gray-500 mt-1">如果你能看到图标，说明CDN加载成功</p>
                    </div>
                    <div class="p-4 bg-gray-50 rounded-lg">
                        <p class="font-medium text-gray-700">
                            <i class="fas fa-wind text-cyan-500 mr-2"></i>
                            Tailwind CSS 3.4.17
                        </p>
                        <p class="text-sm text-gray-500 mt-1">如果样式正常显示，说明CDN加载成功</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 浮动操作按钮 -->
    <div class="fixed bottom-6 right-6">
        <button class="bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-full shadow-lg transition duration-300 transform hover:scale-110">
            <i class="fas fa-arrow-up text-xl"></i>
        </button>
    </div>
</body>
</html>