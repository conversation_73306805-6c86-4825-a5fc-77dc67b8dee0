# Homebrew 中国镜像源配置
export HOMEBREW_BREW_GIT="https://mirrors.tuna.tsinghua.edu.cn/brew.git"
export HOMEBREW_BOTTLE_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles"
export HOMEBREW_API_DOMAIN="https://mirrors.tuna.tsinghua.edu.cn/homebrew-bottles/api"
export HOMEBREW_CORE_GIT="https://mirrors.tuna.tsinghua.edu.cn/homebrew-core.git"
export HOMEBREW_CASK_GIT="https://mirrors.tuna.tsinghua.edu.cn/homebrew-cask.git"
# 禁用自动更新以提高性能
export HOMEBREW_NO_AUTO_UPDATE=1

export PATH="/Library/Frameworks/Python.framework/Versions/3.13/bin:$PATH"
export SSL_CERT_FILE=/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/certifi
/cacert.pem

# Added by Windsurf
export PATH="/Users/<USER>/.codeium/windsurf/bin:$PATH"

# Added by LM Studio CLI (lms)
export PATH="$PATH:/Users/<USER>/.lmstudio/bin"
# End of LM Studio CLI section 