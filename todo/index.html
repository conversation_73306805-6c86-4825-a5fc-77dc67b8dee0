<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>待办事项清单</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-tasks"></i> 我的待办清单</h1>
            <div class="stats">
                <span class="stat-item">
                    <span class="stat-number" id="total-tasks">0</span>
                    <span class="stat-label">总任务</span>
                </span>
                <span class="stat-item">
                    <span class="stat-number" id="completed-tasks">0</span>
                    <span class="stat-label">已完成</span>
                </span>
                <span class="stat-item">
                    <span class="stat-number" id="pending-tasks">0</span>
                    <span class="stat-label">待完成</span>
                </span>
            </div>
        </header>

        <main>
            <div class="add-task-section">
                <div class="search-section">
                    <div class="search-input-group">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="search-input" placeholder="搜索任务...">
                        <button id="clear-search-btn" class="clear-search-btn" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="input-group">
                    <input type="text" id="task-input" placeholder="添加新的待办事项..." maxlength="100">
                    <select id="category-select">
                        <option value="personal">个人</option>
                        <option value="work" selected>工作</option>
                        <option value="study">学习</option>
                        <option value="health">健康</option>
                        <option value="other">其他</option>
                    </select>
                    <select id="priority-select">
                        <option value="low">低优先级</option>
                        <option value="medium" selected>中优先级</option>
                        <option value="high">高优先级</option>
                    </select>
                    <button id="add-task-btn">
                        <i class="fas fa-plus"></i> 添加
                    </button>
                </div>
            </div>

            <div class="filter-section">
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">
                        <i class="fas fa-list"></i> 全部
                    </button>
                    <button class="filter-btn" data-filter="pending">
                        <i class="fas fa-clock"></i> 待完成
                    </button>
                    <button class="filter-btn" data-filter="completed">
                        <i class="fas fa-check"></i> 已完成
                    </button>
                </div>
                <div class="category-filter">
                    <select id="category-filter">
                        <option value="all">所有分类</option>
                        <option value="personal">个人</option>
                        <option value="work">工作</option>
                        <option value="study">学习</option>
                        <option value="health">健康</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="sort-section">
                    <select id="sort-select">
                        <option value="date-desc">按时间降序</option>
                        <option value="date-asc">按时间升序</option>
                        <option value="priority">按优先级</option>
                        <option value="alphabetical">按字母顺序</option>
                        <option value="category">按分类</option>
                    </select>
                </div>
            </div>

            <div class="tasks-container">
                <div id="tasks-list" class="tasks-list">
                    <!-- 任务项将在这里动态生成 -->
                </div>
                <div id="empty-state" class="empty-state">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>暂无待办事项</h3>
                    <p>添加你的第一个任务开始管理你的待办清单吧！</p>
                </div>
            </div>
        </main>

        <footer>
            <div class="bulk-actions">
                <button id="export-btn" class="bulk-btn">
                    <i class="fas fa-download"></i> 导出数据
                </button>
                <button id="import-btn" class="bulk-btn">
                    <i class="fas fa-upload"></i> 导入数据
                </button>
                <button id="clear-completed-btn" class="bulk-btn">
                    <i class="fas fa-trash"></i> 清除已完成
                </button>
                <button id="clear-all-btn" class="bulk-btn danger">
                    <i class="fas fa-trash-alt"></i> 清空全部
                </button>
            </div>
            <div class="keyboard-shortcuts">
                <button id="shortcuts-btn" class="shortcuts-btn">
                    <i class="fas fa-keyboard"></i> 快捷键
                </button>
            </div>
        </footer>
    </div>

    <!-- 确认对话框 -->
    <div id="confirm-modal" class="modal">
        <div class="modal-content">
            <h3>确认操作</h3>
            <p id="confirm-message"></p>
            <div class="modal-actions">
                <button id="confirm-yes" class="btn-primary">确认</button>
                <button id="confirm-no" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 快捷键帮助对话框 -->
    <div id="shortcuts-modal" class="modal">
        <div class="modal-content shortcuts-modal-content">
            <h3><i class="fas fa-keyboard"></i> 键盘快捷键</h3>
            <div class="shortcuts-list">
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>Enter</kbd>
                    <span>快速添加任务</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>F</kbd>
                    <span>搜索任务</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>1</kbd>
                    <span>显示全部任务</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>2</kbd>
                    <span>显示待完成任务</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Ctrl</kbd> + <kbd>3</kbd>
                    <span>显示已完成任务</span>
                </div>
                <div class="shortcut-item">
                    <kbd>Escape</kbd>
                    <span>关闭对话框/取消编辑</span>
                </div>
            </div>
            <div class="modal-actions">
                <button id="shortcuts-close" class="btn-secondary">关闭</button>
            </div>
        </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input type="file" id="import-file-input" accept=".json" style="display: none;">

    <script src="script.js"></script>
</body>
</html>