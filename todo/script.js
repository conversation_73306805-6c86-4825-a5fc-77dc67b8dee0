// To-Do List 应用主要功能
class TodoApp {
    constructor() {
        this.tasks = this.loadTasks();
        this.currentFilter = 'all';
        this.currentSort = 'date-desc';
        this.currentCategory = 'all';
        this.searchQuery = '';
        this.editingTaskId = null;
        this.draggedElement = null;
        
        this.initializeElements();
        this.bindEvents();
        this.render();
    }

    // 初始化DOM元素引用
    initializeElements() {
        this.taskInput = document.getElementById('task-input');
        this.categorySelect = document.getElementById('category-select');
        this.prioritySelect = document.getElementById('priority-select');
        this.addTaskBtn = document.getElementById('add-task-btn');
        this.tasksList = document.getElementById('tasks-list');
        this.emptyState = document.getElementById('empty-state');
        this.filterBtns = document.querySelectorAll('.filter-btn');
        this.categoryFilter = document.getElementById('category-filter');
        this.sortSelect = document.getElementById('sort-select');
        this.searchInput = document.getElementById('search-input');
        this.clearSearchBtn = document.getElementById('clear-search-btn');
        this.exportBtn = document.getElementById('export-btn');
        this.importBtn = document.getElementById('import-btn');
        this.importFileInput = document.getElementById('import-file-input');
        this.clearCompletedBtn = document.getElementById('clear-completed-btn');
        this.clearAllBtn = document.getElementById('clear-all-btn');
        this.shortcutsBtn = document.getElementById('shortcuts-btn');
        this.confirmModal = document.getElementById('confirm-modal');
        this.confirmMessage = document.getElementById('confirm-message');
        this.confirmYes = document.getElementById('confirm-yes');
        this.confirmNo = document.getElementById('confirm-no');
        this.shortcutsModal = document.getElementById('shortcuts-modal');
        this.shortcutsClose = document.getElementById('shortcuts-close');
        
        // 统计元素
        this.totalTasksEl = document.getElementById('total-tasks');
        this.completedTasksEl = document.getElementById('completed-tasks');
        this.pendingTasksEl = document.getElementById('pending-tasks');
    }

    // 绑定事件监听器
    bindEvents() {
        // 添加任务
        this.addTaskBtn.addEventListener('click', () => this.addTask());
        this.taskInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.addTask();
        });

        // 搜索功能
        this.searchInput.addEventListener('input', (e) => {
            this.searchQuery = e.target.value.toLowerCase();
            this.clearSearchBtn.style.display = this.searchQuery ? 'block' : 'none';
            this.render();
        });
        
        this.clearSearchBtn.addEventListener('click', () => {
            this.searchInput.value = '';
            this.searchQuery = '';
            this.clearSearchBtn.style.display = 'none';
            this.render();
        });

        // 过滤任务
        this.filterBtns.forEach(btn => {
            btn.addEventListener('click', () => this.setFilter(btn.dataset.filter));
        });
        
        // 分类过滤
        this.categoryFilter.addEventListener('change', () => {
            this.currentCategory = this.categoryFilter.value;
            this.render();
        });

        // 排序任务
        this.sortSelect.addEventListener('change', () => {
            this.currentSort = this.sortSelect.value;
            this.render();
        });
        
        // 数据导入导出
        this.exportBtn.addEventListener('click', () => this.exportData());
        this.importBtn.addEventListener('click', () => this.importFileInput.click());
        this.importFileInput.addEventListener('change', (e) => this.importData(e));

        // 批量操作
        this.clearCompletedBtn.addEventListener('click', () => this.clearCompleted());
        this.clearAllBtn.addEventListener('click', () => this.clearAll());
        
        // 快捷键帮助
        this.shortcutsBtn.addEventListener('click', () => this.showShortcutsModal());
        this.shortcutsClose.addEventListener('click', () => this.hideShortcutsModal());

        // 模态框
        this.confirmNo.addEventListener('click', () => this.hideModal());
        this.confirmModal.addEventListener('click', (e) => {
            if (e.target === this.confirmModal) this.hideModal();
        });
        
        this.shortcutsModal.addEventListener('click', (e) => {
            if (e.target === this.shortcutsModal) this.hideShortcutsModal();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 添加新任务
    addTask() {
        const text = this.taskInput.value.trim();
        if (!text) {
            this.showNotification('请输入任务内容', 'error');
            return;
        }

        if (text.length > 100) {
            this.showNotification('任务内容不能超过100个字符', 'error');
            return;
        }

        const task = {
            id: this.generateId(),
            text: text,
            completed: false,
            category: this.categorySelect.value,
            priority: this.prioritySelect.value,
            createdAt: new Date().toISOString(),
            completedAt: null
        };

        this.tasks.unshift(task);
        this.saveTasks();
        this.taskInput.value = '';
        this.categorySelect.value = 'work';
        this.prioritySelect.value = 'medium';
        this.render();
        this.showNotification('任务添加成功', 'success');
    }

    // 切换任务完成状态
    toggleTask(id) {
        const task = this.tasks.find(t => t.id === id);
        if (task) {
            task.completed = !task.completed;
            task.completedAt = task.completed ? new Date().toISOString() : null;
            this.saveTasks();
            this.render();
            
            const message = task.completed ? '任务已完成' : '任务已标记为未完成';
            this.showNotification(message, 'success');
        }
    }

    // 删除任务
    deleteTask(id) {
        const task = this.tasks.find(t => t.id === id);
        if (task) {
            this.showConfirmModal(
                `确定要删除任务"${task.text}"吗？`,
                () => {
                    this.tasks = this.tasks.filter(t => t.id !== id);
                    this.saveTasks();
                    this.render();
                    this.showNotification('任务已删除', 'success');
                }
            );
        }
    }

    // 编辑任务
    editTask(id) {
        const task = this.tasks.find(t => t.id === id);
        if (!task) return;

        const taskElement = document.querySelector(`[data-task-id="${id}"]`);
        const taskTextElement = taskElement.querySelector('.task-text');
        const originalText = task.text;

        // 创建编辑输入框
        const input = document.createElement('input');
        input.type = 'text';
        input.value = originalText;
        input.className = 'edit-input';
        input.style.cssText = `
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #667eea;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 500;
            background: white;
        `;

        // 替换文本为输入框
        taskTextElement.replaceWith(input);
        input.focus();
        input.select();

        // 保存编辑
        const saveEdit = () => {
            const newText = input.value.trim();
            if (newText && newText !== originalText) {
                if (newText.length > 100) {
                    this.showNotification('任务内容不能超过100个字符', 'error');
                    return;
                }
                task.text = newText;
                this.saveTasks();
                this.showNotification('任务已更新', 'success');
            }
            this.render();
        };

        // 取消编辑
        const cancelEdit = () => {
            this.render();
        };

        // 绑定事件
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') saveEdit();
            if (e.key === 'Escape') cancelEdit();
        });

        input.addEventListener('blur', saveEdit);
    }

    // 设置过滤器
    setFilter(filter) {
        this.currentFilter = filter;
        
        // 更新按钮状态
        this.filterBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.filter === filter);
        });
        
        this.render();
    }

    // 获取过滤后的任务
    getFilteredTasks() {
        let filtered = [...this.tasks];
        
        // 应用搜索过滤
        if (this.searchQuery) {
            filtered = filtered.filter(task => 
                task.text.toLowerCase().includes(this.searchQuery)
            );
        }
        
        // 应用状态过滤器
        switch (this.currentFilter) {
            case 'pending':
                filtered = filtered.filter(task => !task.completed);
                break;
            case 'completed':
                filtered = filtered.filter(task => task.completed);
                break;
            // 'all' 不需要过滤
        }
        
        // 应用分类过滤
        if (this.currentCategory !== 'all') {
            filtered = filtered.filter(task => task.category === this.currentCategory);
        }
        
        // 应用排序
        switch (this.currentSort) {
            case 'date-asc':
                filtered.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
                break;
            case 'date-desc':
                filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                break;
            case 'priority':
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                filtered.sort((a, b) => {
                    if (a.completed !== b.completed) {
                        return a.completed - b.completed; // 未完成的在前
                    }
                    return priorityOrder[b.priority] - priorityOrder[a.priority];
                });
                break;
            case 'alphabetical':
                filtered.sort((a, b) => a.text.localeCompare(b.text, 'zh-CN'));
                break;
            case 'category':
                filtered.sort((a, b) => {
                    if (a.completed !== b.completed) {
                        return a.completed - b.completed;
                    }
                    return a.category.localeCompare(b.category);
                });
                break;
        }
        
        return filtered;
    }

    // 清除已完成任务
    clearCompleted() {
        const completedCount = this.tasks.filter(task => task.completed).length;
        if (completedCount === 0) {
            this.showNotification('没有已完成的任务', 'info');
            return;
        }

        this.showConfirmModal(
            `确定要清除所有 ${completedCount} 个已完成的任务吗？`,
            () => {
                this.tasks = this.tasks.filter(task => !task.completed);
                this.saveTasks();
                this.render();
                this.showNotification(`已清除 ${completedCount} 个已完成任务`, 'success');
            }
        );
    }

    // 清空所有任务
    clearAll() {
        if (this.tasks.length === 0) {
            this.showNotification('没有任务需要清除', 'info');
            return;
        }

        this.showConfirmModal(
            `确定要清空所有 ${this.tasks.length} 个任务吗？此操作不可恢复！`,
            () => {
                this.tasks = [];
                this.saveTasks();
                this.render();
                this.showNotification('所有任务已清空', 'success');
            }
        );
    }

    // 显示确认模态框
    showConfirmModal(message, onConfirm) {
        this.confirmMessage.textContent = message;
        this.confirmModal.classList.add('show');
        
        // 移除之前的事件监听器
        this.confirmYes.replaceWith(this.confirmYes.cloneNode(true));
        this.confirmYes = document.getElementById('confirm-yes');
        
        this.confirmYes.addEventListener('click', () => {
            this.hideModal();
            onConfirm();
        });
    }

    // 隐藏模态框
    hideModal() {
        this.confirmModal.classList.remove('show');
    }
    
    // 显示快捷键帮助
    showShortcutsModal() {
        this.shortcutsModal.classList.add('show');
    }
    
    // 隐藏快捷键帮助
    hideShortcutsModal() {
        this.shortcutsModal.classList.remove('show');
    }
    
    // 处理键盘快捷键
    handleKeyboardShortcuts(e) {
        // ESC键关闭模态框
        if (e.key === 'Escape') {
            this.hideModal();
            this.hideShortcutsModal();
            return;
        }
        
        // 如果正在编辑或在输入框中，不处理其他快捷键
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
            return;
        }
        
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'Enter':
                    e.preventDefault();
                    this.taskInput.focus();
                    break;
                case 'f':
                    e.preventDefault();
                    this.searchInput.focus();
                    break;
                case '1':
                    e.preventDefault();
                    this.setFilter('all');
                    break;
                case '2':
                    e.preventDefault();
                    this.setFilter('pending');
                    break;
                case '3':
                    e.preventDefault();
                    this.setFilter('completed');
                    break;
            }
        }
    }
    
    // 导出数据
    exportData() {
        try {
            const data = {
                tasks: this.tasks,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };
            
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `todo-backup-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            this.showNotification('数据导出成功', 'success');
        } catch (error) {
            console.error('导出失败:', error);
            this.showNotification('导出失败，请重试', 'error');
        }
    }
    
    // 导入数据
    importData(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                
                if (!data.tasks || !Array.isArray(data.tasks)) {
                    throw new Error('无效的数据格式');
                }
                
                this.showConfirmModal(
                    `确定要导入 ${data.tasks.length} 个任务吗？这将替换当前所有数据！`,
                    () => {
                        this.tasks = data.tasks.map(task => ({
                            ...task,
                            category: task.category || 'other' // 兼容旧版本
                        }));
                        this.saveTasks();
                        this.render();
                        this.showNotification(`成功导入 ${data.tasks.length} 个任务`, 'success');
                    }
                );
            } catch (error) {
                console.error('导入失败:', error);
                this.showNotification('导入失败，请检查文件格式', 'error');
            }
        };
        
        reader.readAsText(file);
        event.target.value = ''; // 清空文件选择
    }

    // 显示通知
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // 样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 500;
            z-index: 2000;
            animation: slideInRight 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        // 根据类型设置颜色
        switch (type) {
            case 'success':
                notification.style.background = 'linear-gradient(135deg, #48bb78, #38a169)';
                break;
            case 'error':
                notification.style.background = 'linear-gradient(135deg, #f56565, #e53e3e)';
                break;
            case 'info':
                notification.style.background = 'linear-gradient(135deg, #4299e1, #3182ce)';
                break;
        }
        
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 更新统计信息
    updateStats() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(task => task.completed).length;
        const pending = total - completed;
        
        this.totalTasksEl.textContent = total;
        this.completedTasksEl.textContent = completed;
        this.pendingTasksEl.textContent = pending;
    }

    // 格式化日期
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) {
            return '今天';
        } else if (diffDays === 2) {
            return '昨天';
        } else if (diffDays <= 7) {
            return `${diffDays - 1}天前`;
        } else {
            return date.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric'
            });
        }
    }

    // 获取优先级显示文本
    getPriorityText(priority) {
        const priorityMap = {
            high: '高优先级',
            medium: '中优先级',
            low: '低优先级'
        };
        return priorityMap[priority] || '中优先级';
    }
    
    // 获取分类显示文本
    getCategoryText(category) {
        const categoryMap = {
            personal: '个人',
            work: '工作',
            study: '学习',
            health: '健康',
            other: '其他'
        };
        return categoryMap[category] || '其他';
    }

    // 渲染任务列表
    render() {
        const filteredTasks = this.getFilteredTasks();
        
        // 更新统计
        this.updateStats();
        
        // 清空任务列表
        this.tasksList.innerHTML = '';
        
        if (filteredTasks.length === 0) {
            this.emptyState.style.display = 'block';
            return;
        }
        
        this.emptyState.style.display = 'none';
        
        // 渲染每个任务
        filteredTasks.forEach(task => {
            const taskElement = document.createElement('div');
            taskElement.className = `task-item ${task.completed ? 'completed' : ''}`;
            taskElement.setAttribute('data-task-id', task.id);
            
            taskElement.innerHTML = `
                <div class="drag-handle" title="拖拽排序">
                    <i class="fas fa-grip-vertical"></i>
                </div>
                <input type="checkbox" class="task-checkbox" ${task.completed ? 'checked' : ''}>
                <div class="task-content">
                    <div class="task-text">${this.escapeHtml(task.text)}</div>
                    <div class="task-meta">
                        <span class="category-badge category-${task.category}">
                            ${this.getCategoryText(task.category)}
                        </span>
                        <span class="priority-badge priority-${task.priority}">
                            ${this.getPriorityText(task.priority)}
                        </span>
                        <span class="task-date">
                            <i class="fas fa-clock"></i>
                            ${this.formatDate(task.createdAt)}
                        </span>
                        ${task.completed ? `
                            <span class="completed-date">
                                <i class="fas fa-check"></i>
                                ${this.formatDate(task.completedAt)}
                            </span>
                        ` : ''}
                    </div>
                </div>
                <div class="task-actions">
                    <button class="task-btn edit-btn" title="编辑任务">
                        <i class="fas fa-edit"></i>
                        编辑
                    </button>
                    <button class="task-btn delete-btn" title="删除任务">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                </div>
            `;
            
            // 添加拖拽功能
            this.addDragFunctionality(taskElement, task.id);
            
            // 绑定事件
            const checkbox = taskElement.querySelector('.task-checkbox');
            const editBtn = taskElement.querySelector('.edit-btn');
            const deleteBtn = taskElement.querySelector('.delete-btn');
            
            checkbox.addEventListener('change', () => this.toggleTask(task.id));
            editBtn.addEventListener('click', () => this.editTask(task.id));
            deleteBtn.addEventListener('click', () => this.deleteTask(task.id));
            
            this.tasksList.appendChild(taskElement);
        });
    }
    
    // 添加拖拽功能
    addDragFunctionality(taskElement, taskId) {
        const dragHandle = taskElement.querySelector('.drag-handle');
        
        dragHandle.addEventListener('mousedown', (e) => {
            e.preventDefault();
            this.startDrag(taskElement, taskId, e);
        });
        
        // 触摸设备支持
        dragHandle.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.startDrag(taskElement, taskId, e.touches[0]);
        });
    }
    
    // 开始拖拽
    startDrag(element, taskId, event) {
        this.draggedElement = element;
        this.draggedTaskId = taskId;
        
        element.classList.add('dragging');
        
        const rect = element.getBoundingClientRect();
        const offsetY = event.clientY - rect.top;
        
        const placeholder = document.createElement('div');
        placeholder.className = 'drag-placeholder';
        placeholder.style.height = rect.height + 'px';
        
        const mouseMoveHandler = (e) => {
            const clientY = e.clientY || (e.touches && e.touches[0].clientY);
            this.handleDragMove(clientY, offsetY, placeholder);
        };
        
        const mouseUpHandler = () => {
            this.endDrag(placeholder);
            document.removeEventListener('mousemove', mouseMoveHandler);
            document.removeEventListener('mouseup', mouseUpHandler);
            document.removeEventListener('touchmove', mouseMoveHandler);
            document.removeEventListener('touchend', mouseUpHandler);
        };
        
        document.addEventListener('mousemove', mouseMoveHandler);
        document.addEventListener('mouseup', mouseUpHandler);
        document.addEventListener('touchmove', mouseMoveHandler);
        document.addEventListener('touchend', mouseUpHandler);
    }
    
    // 处理拖拽移动
    handleDragMove(clientY, offsetY, placeholder) {
        if (!this.draggedElement) return;
        
        const taskItems = Array.from(this.tasksList.children).filter(el => 
            el !== this.draggedElement && !el.classList.contains('drag-placeholder')
        );
        
        let insertAfter = null;
        
        for (const item of taskItems) {
            const rect = item.getBoundingClientRect();
            const itemCenter = rect.top + rect.height / 2;
            
            if (clientY - offsetY < itemCenter) {
                insertAfter = item;
                break;
            }
        }
        
        if (placeholder.parentNode) {
            placeholder.parentNode.removeChild(placeholder);
        }
        
        if (insertAfter) {
            this.tasksList.insertBefore(placeholder, insertAfter);
        } else {
            this.tasksList.appendChild(placeholder);
        }
    }
    
    // 结束拖拽
    endDrag(placeholder) {
        if (!this.draggedElement || !placeholder.parentNode) return;
        
        const newIndex = Array.from(this.tasksList.children).indexOf(placeholder);
        const filteredTasks = this.getFilteredTasks();
        const draggedTask = this.tasks.find(t => t.id === this.draggedTaskId);
        const oldIndex = filteredTasks.findIndex(t => t.id === this.draggedTaskId);
        
        if (draggedTask && oldIndex !== -1 && newIndex !== oldIndex) {
            // 重新排序任务
            const originalIndex = this.tasks.findIndex(t => t.id === this.draggedTaskId);
            this.tasks.splice(originalIndex, 1);
            
            // 计算新位置在原始数组中的索引
            let targetIndex = 0;
            if (newIndex < filteredTasks.length) {
                const targetTask = filteredTasks[newIndex];
                targetIndex = this.tasks.findIndex(t => t.id === targetTask.id);
            } else {
                targetIndex = this.tasks.length;
            }
            
            this.tasks.splice(targetIndex, 0, draggedTask);
            this.saveTasks();
        }
        
        this.draggedElement.classList.remove('dragging');
        placeholder.parentNode.removeChild(placeholder);
        this.draggedElement = null;
        this.draggedTaskId = null;
        
        this.render();
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 保存任务到本地存储
    saveTasks() {
        try {
            localStorage.setItem('todoTasks', JSON.stringify(this.tasks));
        } catch (error) {
            console.error('保存任务失败:', error);
            this.showNotification('保存失败，请检查浏览器存储空间', 'error');
        }
    }

    // 从本地存储加载任务
    loadTasks() {
        try {
            const saved = localStorage.getItem('todoTasks');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('加载任务失败:', error);
            return [];
        }
    }
}

// 添加通知动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
`;
document.head.appendChild(style);

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new TodoApp();
});