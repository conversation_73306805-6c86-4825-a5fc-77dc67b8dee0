/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

header h1 {
    font-size: 2.5rem;
    color: #4a5568;
    margin-bottom: 20px;
    font-weight: 700;
}

header h1 i {
    color: #667eea;
    margin-right: 10px;
}

.stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 15px;
    color: white;
    min-width: 80px;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 主要内容区域 */
main {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: 20px;
}

/* 添加任务区域 */
.add-task-section {
    margin-bottom: 30px;
}

/* 搜索区域 */
.search-section {
    margin-bottom: 20px;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 400px;
    margin: 0 auto;
}

.search-icon {
    position: absolute;
    left: 15px;
    color: #718096;
    z-index: 1;
}

#search-input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    font-size: 1rem;
    background: #f8fafc;
    transition: all 0.3s ease;
}

#search-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-search-btn {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.clear-search-btn:hover {
    background: #e2e8f0;
    color: #4a5568;
}

.input-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

#task-input {
    flex: 1;
    min-width: 250px;
    padding: 15px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8fafc;
}

#task-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#category-select,
#priority-select {
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: #f8fafc;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

#category-select:focus,
#priority-select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
}

#add-task-btn {
    padding: 15px 25px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

#add-task-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

#add-task-btn:active {
    transform: translateY(0);
}

/* 过滤和排序区域 */
.filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid #e2e8f0;
    background: #f8fafc;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-btn:hover {
    border-color: #667eea;
    background: #edf2f7;
}

.filter-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

#category-filter,
#sort-select {
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: #f8fafc;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

#category-filter:focus,
#sort-select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
}

.category-filter {
    display: flex;
    align-items: center;
}

/* 任务列表 */
.tasks-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.task-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    transition: all 0.3s ease;
    animation: slideIn 0.3s ease;
    position: relative;
}

.task-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.drag-placeholder {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: 2px dashed #667eea;
    border-radius: 15px;
    margin: 8px 0;
    opacity: 0.5;
}

.drag-handle {
    cursor: grab;
    color: #cbd5e0;
    padding: 5px;
    margin-right: 10px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.drag-handle:hover {
    color: #667eea;
    background: #edf2f7;
}

.drag-handle:active {
    cursor: grabbing;
}

.task-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #cbd5e0;
}

.task-item.completed {
    background: #f0fff4;
    border-color: #68d391;
    opacity: 0.8;
}

.task-item.completed .task-text {
    text-decoration: line-through;
    color: #68d391;
}

.task-checkbox {
    width: 20px;
    height: 20px;
    margin-right: 15px;
    cursor: pointer;
    accent-color: #667eea;
}

.task-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.task-text {
    font-size: 1.1rem;
    font-weight: 500;
    word-break: break-word;
}

.task-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85rem;
    color: #718096;
    align-items: center;
}

.priority-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-high {
    background: #fed7d7;
    color: #c53030;
}

.priority-medium {
    background: #feebc8;
    color: #dd6b20;
}

.priority-low {
    background: #c6f6d5;
    color: #38a169;
}

/* 分类标签样式 */
.category-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 8px;
}

.category-personal {
    background: #e6fffa;
    color: #234e52;
}

.category-work {
    background: #ebf8ff;
    color: #2a4365;
}

.category-study {
    background: #f0fff4;
    color: #22543d;
}

.category-health {
    background: #fef5e7;
    color: #744210;
}

.category-other {
    background: #faf5ff;
    color: #553c9a;
}

.task-actions {
    display: flex;
    gap: 10px;
}

.task-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.edit-btn {
    background: #bee3f8;
    color: #2b6cb0;
}

.edit-btn:hover {
    background: #90cdf4;
}

.delete-btn {
    background: #fed7d7;
    color: #c53030;
}

.delete-btn:hover {
    background: #feb2b2;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #cbd5e0;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #4a5568;
}

/* 底部操作区域 */
footer {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.keyboard-shortcuts {
    margin-top: 15px;
    text-align: center;
}

.shortcuts-btn {
    padding: 8px 15px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    color: #4a5568;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.shortcuts-btn:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.bulk-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.bulk-btn {
    padding: 12px 20px;
    border: 2px solid #e2e8f0;
    background: #f8fafc;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.bulk-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.bulk-btn.danger {
    border-color: #fed7d7;
    background: #fed7d7;
    color: #c53030;
}

.bulk-btn.danger:hover {
    background: #feb2b2;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 20px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: slideUp 0.3s ease;
}

.shortcuts-modal-content {
    max-width: 500px;
    text-align: left;
}

.shortcuts-list {
    margin: 20px 0;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
}

.shortcut-item:last-child {
    border-bottom: none;
}

kbd {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 0.8rem;
    font-family: monospace;
    margin: 0 2px;
}

.shortcut-item span {
    color: #4a5568;
    font-size: 0.9rem;
}

.modal-content h3 {
    margin-bottom: 15px;
    color: #4a5568;
    font-size: 1.3rem;
}

.modal-content p {
    margin-bottom: 25px;
    color: #718096;
    line-height: 1.5;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn-primary {
    padding: 12px 25px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    padding: 12px 25px;
    background: #f8fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

/* 动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
/* 高亮搜索结果 */
.search-highlight {
    background: linear-gradient(135deg, #fef5e7, #fed7aa);
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

/* 任务统计动画 */
.stat-number {
    transition: all 0.3s ease;
}

.stat-number.updated {
    transform: scale(1.2);
    color: #667eea;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 性能优化：减少重绘 */
.task-item,
.filter-btn,
.bulk-btn {
    will-change: transform;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header {
        padding: 20px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .stats {
        gap: 15px;
    }
    
    .stat-item {
        min-width: 70px;
        padding: 12px;
    }
    
    main {
        padding: 20px;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    #task-input {
        min-width: auto;
    }
    
    .search-input-group {
        max-width: none;
    }
    
    .filter-section {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .filter-buttons {
        justify-content: center;
    }
    
    .category-filter {
        justify-content: center;
    }
    
    .task-item {
        padding: 15px;
    }
    
    .drag-handle {
        margin-right: 5px;
    }
    
    .task-meta {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .category-badge,
    .priority-badge {
        font-size: 0.7rem;
        padding: 2px 6px;
    }
    
    .task-actions {
        flex-direction: column;
    }
    
    .bulk-actions {
        flex-direction: column;
    }
    
    .modal-content {
        padding: 20px;
        margin: 20px;
    }
    
    .modal-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.8rem;
    }
    
    .stats {
        flex-direction: column;
        align-items: center;
    }
    
    .stat-item {
        width: 100%;
        max-width: 200px;
    }
    
    .filter-buttons {
        flex-direction: column;
    }
    
    .filter-btn {
        text-align: center;
    }
}