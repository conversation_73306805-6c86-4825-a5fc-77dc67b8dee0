---
description: To-Do List 项目开发规则和约定
globs: ["**/*"]
alwaysApply: true
---

# To-Do List 项目规则

## 技术栈
- **前端**: 原生 HTML5 + CSS3 + JavaScript (ES6+)
- **样式**: CSS Grid/Flexbox + CSS 自定义属性
- **图标**: Font Awesome 6.0
- **存储**: localStorage (浏览器本地存储)
- **响应式**: Mobile-first 设计

## 编码规范

### HTML
- 使用语义化标签
- 所有属性使用双引号
- 合理使用 ARIA 属性提升可访问性
- 中文页面使用 `lang="zh-CN"`

### CSS
- 使用 BEM 命名约定的变体
- 优先使用 Flexbox 和 Grid 布局
- 使用 CSS 自定义属性管理颜色和间距
- 移动端优先的响应式设计
- 使用渐变和阴影创建现代视觉效果

### JavaScript
- 使用 ES6+ 语法 (类、箭头函数、模板字符串)
- 采用面向对象编程模式
- 所有用户输入进行 HTML 转义
- 使用 try-catch 处理 localStorage 异常
- 事件委托优化性能

## 功能特性

### 核心功能
- ✅ 添加/删除/编辑任务
- ✅ 标记任务完成状态
- ✅ 任务分类系统 (个人/工作/学习/健康/其他)
- ✅ 任务优先级设置 (高/中/低)
- ✅ 任务过滤 (全部/待完成/已完成)
- ✅ 分类过滤功能
- ✅ 实时搜索功能
- ✅ 任务排序 (时间/优先级/字母/分类)
- ✅ 拖拽排序功能
- ✅ 批量操作 (清除已完成/清空全部)
- ✅ 数据导入/导出功能
- ✅ 本地数据持久化

### 用户体验
- ✅ 实时统计显示
- ✅ 操作确认对话框
- ✅ 成功/错误通知提示
- ✅ 增强的键盘快捷键支持
- ✅ 快捷键帮助对话框
- ✅ 响应式设计
- ✅ 平滑动画过渡
- ✅ 拖拽交互体验
- ✅ 搜索结果高亮
- ✅ 分类标签可视化

## 测试策略

### 功能测试
- 任务 CRUD 操作验证
- 过滤和排序功能测试
- 本地存储数据完整性
- 边界条件处理 (空输入、超长文本)

### 兼容性测试
- 现代浏览器支持 (Chrome 80+, Firefox 75+, Safari 13+)
- 移动设备响应式测试
- localStorage 可用性检测

## 项目约定

### 文件结构
```
todo/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # 主要逻辑
└── .trae/
    └── rules/
        └── project_rules.md
```

### 数据结构
```javascript
// 任务对象结构
{
  id: string,           // 唯一标识符
  text: string,         // 任务内容 (最大100字符)
  completed: boolean,   // 完成状态
  category: string,     // 任务分类 (personal/work/study/health/other)
  priority: string,     // 优先级 (high/medium/low)
  createdAt: string,    // 创建时间 (ISO格式)
  completedAt: string   // 完成时间 (ISO格式)
}

// 导出数据结构
{
  tasks: Array,         // 任务数组
  exportDate: string,   // 导出时间
  version: string       // 数据版本
}
```

### 性能优化
- 使用文档片段减少 DOM 操作
- 事件委托避免大量事件监听器
- CSS 动画使用 transform 和 opacity
- will-change 属性优化重绘性能
- 拖拽操作使用 requestAnimationFrame
- 搜索防抖优化
- 图片使用 SVG 格式 (如需要)

### 交互优化
- 拖拽排序支持鼠标和触摸设备
- 键盘快捷键全面支持
- 搜索实时反馈
- 分类标签颜色编码
- 操作状态视觉反馈
- 加载状态指示器

### 安全考虑
- 用户输入 HTML 转义
- localStorage 异常处理
- XSS 防护措施
- 文件导入格式验证
- 数据完整性检查

### 键盘快捷键
- `Ctrl/Cmd + Enter`: 快速添加任务
- `Ctrl/Cmd + F`: 搜索任务
- `Ctrl/Cmd + 1`: 显示全部任务
- `Ctrl/Cmd + 2`: 显示待完成任务
- `Ctrl/Cmd + 3`: 显示已完成任务
- `Escape`: 关闭对话框/取消编辑

### 新增功能说明

#### 任务分类系统
- 5个预设分类：个人、工作、学习、健康、其他
- 分类标签颜色编码便于识别
- 支持按分类过滤和排序

#### 搜索功能
- 实时搜索任务内容
- 搜索结果高亮显示
- 支持清空搜索

#### 拖拽排序
- 支持鼠标拖拽排序
- 触摸设备兼容
- 视觉反馈和占位符

#### 数据管理
- JSON格式导出备份
- 支持数据导入恢复
- 版本兼容性处理

## 部署说明
- 纯静态文件，可直接在浏览器中打开
- 支持任何静态文件托管服务
- 无需构建步骤或依赖安装