#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MP4转M4A转换脚本
"""

import os
import sys
from pathlib import Path

def test_script_exists():
    """测试脚本文件是否存在"""
    scripts = ['mp4_to_m4a.py', 'mp4_to_m4a.sh']
    for script in scripts:
        if Path(script).exists():
            print(f"✓ {script} 存在")
        else:
            print(f"✗ {script} 不存在")

def test_permissions():
    """测试脚本是否有执行权限"""
    scripts = ['mp4_to_m4a.py', 'mp4_to_m4a.sh']
    for script in scripts:
        if Path(script).exists():
            if os.access(script, os.X_OK):
                print(f"✓ {script} 有执行权限")
            else:
                print(f"✗ {script} 没有执行权限")

def test_input_file():
    """测试输入文件是否存在"""
    input_file = '25714761075-1-16.mp4'
    if Path(input_file).exists():
        print(f"✓ 输入文件 {input_file} 存在")
        file_size = Path(input_file).stat().st_size / (1024 * 1024)
        print(f"  文件大小: {file_size:.1f} MB")
    else:
        print(f"✗ 输入文件 {input_file} 不存在")

def main():
    print("MP4转M4A转换工具测试")
    print("=" * 30)
    
    test_script_exists()
    print()
    
    test_permissions()
    print()
    
    test_input_file()
    print()
    
    print("注意：要实际进行转换，需要先安装ffmpeg工具")
    print("安装命令：brew install ffmpeg")

if __name__ == '__main__':
    main() 