# MP4转M4A转换工具

这个工具包含两个脚本，用于将MP4视频文件转换为M4A音频文件。

## 文件说明

- `mp4_to_m4a.py` - Python版本的转换脚本
- `mp4_to_m4a.sh` - Bash版本的转换脚本
- `README.md` - 使用说明文档

## 前置要求

在使用这些脚本之前，需要先安装ffmpeg工具：

### macOS
```bash
# 使用Homebrew安装
brew install ffmpeg

# 如果Homebrew有问题，可以尝试使用MacPorts
sudo port install ffmpeg
```

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install ffmpeg
```

### Windows
从 [FFmpeg官网](https://ffmpeg.org/download.html) 下载并安装

## 使用方法

### Python版本 (mp4_to_m4a.py)

#### 基本用法
```bash
# 转换单个文件
python3 mp4_to_m4a.py input.mp4

# 指定输出文件名
python3 mp4_to_m4a.py input.mp4 -o output.m4a

# 指定音频质量
python3 mp4_to_m4a.py input.mp4 -q 192k

# 批量转换目录中的所有MP4文件
python3 mp4_to_m4a.py /path/to/videos -b

# 批量转换并指定输出目录
python3 mp4_to_m4a.py /path/to/videos -b -o /path/to/output
```

#### 参数说明
- `input` - 输入文件或目录路径
- `-o, --output` - 输出文件或目录路径
- `-q, --quality` - 音频质量（默认：128k）
- `-b, --batch` - 批量转换模式

### Bash版本 (mp4_to_m4a.sh)

#### 基本用法
```bash
# 转换单个文件
./mp4_to_m4a.sh input.mp4

# 指定输出文件名
./mp4_to_m4a.sh input.mp4 output.m4a

# 指定音频质量
./mp4_to_m4a.sh input.mp4 output.m4a 192k

# 批量转换
./mp4_to_m4a.sh -b /path/to/videos

# 批量转换并指定输出目录
./mp4_to_m4a.sh -b /path/to/videos /path/to/output

# 显示帮助信息
./mp4_to_m4a.sh -h
```

## 音频质量选项

常用的音频质量设置：
- `96k` - 低质量，文件较小
- `128k` - 标准质量（默认）
- `192k` - 高质量
- `256k` - 很高质量
- `320k` - 最高质量

## 示例

假设你有一个名为 `25714761075-1-16.mp4` 的文件：

```bash
# 使用Python脚本转换
python3 mp4_to_m4a.py 25714761075-1-16.mp4

# 使用Bash脚本转换
./mp4_to_m4a.sh 25714761075-1-16.mp4

# 转换后会生成 25714761075-1-16.m4a 文件
```

## 注意事项

1. 确保输入文件存在且为有效的MP4格式
2. 转换过程中会覆盖同名的输出文件
3. 批量转换时会跳过已经是M4A格式的文件
4. 转换过程可能需要一些时间，取决于文件大小和系统性能

## 故障排除

如果遇到问题：

1. **ffmpeg未找到**：确保已正确安装ffmpeg并添加到系统PATH
2. **权限错误**：确保脚本有执行权限 (`chmod +x script_name`)
3. **文件不存在**：检查输入文件路径是否正确
4. **转换失败**：检查输入文件是否损坏或格式不支持 